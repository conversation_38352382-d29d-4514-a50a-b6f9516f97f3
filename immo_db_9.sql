-- =====================
-- 1. Table Definitions (Entities)
-- =====================
-- Schema: public

-- Table: agents (Gestion des Collaborateurs & Équipes)
CREATE TABLE agents (
    agent_id SERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone_number VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL, -- Stored securely (hashed)
    role VARCHAR(50) NOT NULL DEFAULT 'agent', -- e.g., 'agent', 'manager', 'admin', 'comptable'
    is_active BOOLEAN DEFAULT TRUE,
    hire_date DATE DEFAULT CURRENT_DATE,
    last_login TIMESTAMP,
    commission_rate DECIMAL(5, 2) DEFAULT 0.00, -- Base commission rate
    objectives TEXT, -- Store objectives as JSONB or text
    agency_id INT REFERENCES agencies(agency_id) -- For multi-agency support
);

-- Table: agencies (Gestion Multi-Agences)
CREATE TABLE agencies (
    agency_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address TEXT,
    phone_number VARCHAR(20),
    email VARCHAR(100) UNIQUE,
    website VARCHAR(255),
    logo_url VARCHAR(255),
    creation_date DATE DEFAULT CURRENT_DATE
);

-- Table: clients (Gestion de la Relation Client - CRM)
CREATE TABLE clients (
    client_id SERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone_number VARCHAR(20),
    client_type VARCHAR(50) NOT NULL, -- e.g., 'acquirer', 'seller', 'tenant', 'landlord', 'investor'
    address TEXT,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_interaction TIMESTAMP,
    preferences JSONB, -- Store criteria, budget, specific needs as JSONB
    notes TEXT,
    lead_score DECIMAL(5, 2) DEFAULT 0.00, -- For lead scoring
    lead_source VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    fk_agent_id INT REFERENCES agents(agent_id) -- Agent responsible for this client
);

-- Table: properties (Gestion des Biens Immobiliers)
CREATE TABLE properties (
    property_id SERIAL PRIMARY KEY,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    zip_code VARCHAR(10),
    country VARCHAR(50) NOT NULL DEFAULT 'Benin', -- Default to current country
    latitude DECIMAL(9, 6),
    longitude DECIMAL(9, 6),
    property_type VARCHAR(50) NOT NULL, -- e.g., 'house', 'apartment', 'land', 'commercial', 'industrial'
    transaction_type VARCHAR(50) NOT NULL, -- e.g., 'sale', 'rent', 'seasonal_rent'
    price DECIMAL(15, 2), -- Sale price or monthly rent
    area_sqm DECIMAL(10, 2),
    number_of_rooms INT,
    number_of_bedrooms INT,
    number_of_bathrooms INT,
    description TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'available', -- e.g., 'available', 'under_offer', 'sold', 'rented', 'reserved', 'in_progress'
    dpe_score VARCHAR(10), -- e.g., 'A', 'B', 'C', 'D', 'E', 'F', 'G'
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fk_agent_id INT REFERENCES agents(agent_id), -- Agent responsible for this property
    fk_owner_client_id INT REFERENCES clients(client_id) -- Link to owner client
);

-- Table: property_features (Detailed Property Characteristics)
CREATE TABLE property_features (
    feature_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id) ON DELETE CASCADE,
    feature_name VARCHAR(100) NOT NULL, -- e.g., 'parking', 'elevator', 'garden', 'pool', 'balcony'
    feature_value TEXT -- e.g., 'yes', 'no', '2 spaces'
);

-- Table: property_media (Photos, Videos, 3D tours)
CREATE TABLE property_media (
    media_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id) ON DELETE CASCADE,
    media_url VARCHAR(255) NOT NULL,
    media_type VARCHAR(20) NOT NULL, -- e.g., 'photo', 'video', '3d_tour', 'plan_2d', 'plan_3d'
    description TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: property_diagnostics (Diagnostics Immobiliers Obligatoires)
CREATE TABLE property_diagnostics (
    diagnostic_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id) ON DELETE CASCADE,
    diagnostic_type VARCHAR(50) NOT NULL, -- e.g., 'DPE', 'Amiante', 'Plomb', 'Gaz', 'Electricite', 'ERP'
    expiration_date DATE,
    diagnostic_file_url VARCHAR(255),
    issued_date DATE,
    notes TEXT
);

-- Table: mandates (Gestion des Mandats)
CREATE TABLE mandates (
    mandate_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id) ON DELETE CASCADE,
    client_id INT REFERENCES clients(client_id), -- Owner/Landlord
    agent_id INT REFERENCES agents(agent_id),
    mandate_type VARCHAR(50) NOT NULL, -- e.g., 'simple', 'exclusive', 'semi_exclusive', 'co_exclusive', 'management'
    start_date DATE NOT NULL,
    end_date DATE,
    commission_percentage DECIMAL(5, 2),
    mandate_status VARCHAR(50) NOT NULL DEFAULT 'active', -- e.g., 'active', 'expired', 'renewed', 'cancelled'
    document_url VARCHAR(255), -- Stored PDF or signed document
    signed_date DATE
);

-- Table: appointments (Gestion des Visites & Agenda)
CREATE TABLE appointments (
    appointment_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    client_id INT REFERENCES clients(client_id), -- Client visiting or involved
    agent_id INT REFERENCES agents(agent_id),
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    appointment_type VARCHAR(50) NOT NULL, -- e.g., 'visit', 'meeting', 'call'
    status VARCHAR(50) NOT NULL DEFAULT 'scheduled', -- e.g., 'scheduled', 'completed', 'cancelled', 'rescheduled'
    notes TEXT,
    feedback TEXT, -- Client/agent feedback post-appointment
    is_online_booking BOOLEAN DEFAULT FALSE
);

-- Table: visit_checklists (Check-list de Visite Numérique)
CREATE TABLE visit_checklists (
    checklist_id SERIAL PRIMARY KEY,
    appointment_id INT REFERENCES appointments(appointment_id) ON DELETE CASCADE,
    item_description TEXT NOT NULL,
    is_checked BOOLEAN DEFAULT FALSE,
    comments TEXT
);

-- Table: offers (Suivi des Offres)
CREATE TABLE offers (
    offer_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    client_id INT REFERENCES clients(client_id), -- Buyer/Tenant making the offer
    agent_id INT REFERENCES agents(agent_id),
    offer_amount DECIMAL(15, 2) NOT NULL,
    offer_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiration_date TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- e.g., 'pending', 'accepted', 'rejected', 'countered', 'withdrawn'
    conditions TEXT, -- e.g., 'subject to financing', 'subject to inspection'
    counter_offer_id INT REFERENCES offers(offer_id) -- For chaining counter-offers
);

-- Table: transactions (Gestion des Transactions)
CREATE TABLE transactions (
    transaction_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    buyer_client_id INT REFERENCES clients(client_id),
    seller_client_id INT REFERENCES clients(client_id),
    agent_id INT REFERENCES agents(agent_id),
    transaction_type VARCHAR(50) NOT NULL, -- 'sale' or 'rental'
    final_price DECIMAL(15, 2), -- Final sale price or first month's rent
    transaction_date DATE DEFAULT CURRENT_DATE,
    status VARCHAR(50) NOT NULL DEFAULT 'in_progress', -- e.g., 'in_progress', 'completed', 'cancelled'
    commission_amount DECIMAL(15, 2),
    notes TEXT
);

-- Table: transaction_steps (Etapes Transactionnelles)
CREATE TABLE transaction_steps (
    step_id SERIAL PRIMARY KEY,
    transaction_id INT REFERENCES transactions(transaction_id) ON DELETE CASCADE,
    step_name VARCHAR(100) NOT NULL, -- e.g., 'Offer Accepted', 'Compromise Signed', 'Financing Obtained', 'Notary Act', 'Keys Handed Over'
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'skipped'
    completion_date DATE,
    notes TEXT,
    due_date DATE
);

-- Table: contracts (Documents & Juridique - Général)
CREATE TABLE contracts (
    contract_id SERIAL PRIMARY KEY,
    transaction_id INT REFERENCES transactions(transaction_id),
    mandate_id INT REFERENCES mandates(mandate_id),
    property_id INT REFERENCES properties(property_id),
    client_id INT REFERENCES clients(client_id), -- Main client for the contract (e.g., tenant for lease)
    contract_type VARCHAR(100) NOT NULL, -- e.g., 'lease_agreement', 'sales_agreement', 'compromise_of_sale'
    start_date DATE,
    end_date DATE,
    document_url VARCHAR(255) NOT NULL, -- Link to stored document
    is_signed BOOLEAN DEFAULT FALSE,
    signed_date TIMESTAMP,
    signature_provider VARCHAR(50), -- e.g., 'DocuSign', 'Yousign'
    version_number INT DEFAULT 1,
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: contract_attachments (Documents associés aux contrats)
CREATE TABLE contract_attachments (
    attachment_id SERIAL PRIMARY KEY,
    contract_id INT REFERENCES contracts(contract_id) ON DELETE CASCADE,
    file_url VARCHAR(255) NOT NULL,
    file_type VARCHAR(50), -- e.g., 'PDF', 'JPEG'
    description TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: rental_agreements (Gestion des Locations - Baux)
CREATE TABLE rental_agreements (
    rental_id SERIAL PRIMARY KEY,
    contract_id INT REFERENCES contracts(contract_id) ON DELETE CASCADE,
    property_id INT REFERENCES properties(property_id),
    tenant_client_id INT REFERENCES clients(client_id),
    landlord_client_id INT REFERENCES clients(client_id),
    rent_amount DECIMAL(10, 2) NOT NULL,
    charges_amount DECIMAL(10, 2) DEFAULT 0.00,
    deposit_amount DECIMAL(10, 2),
    start_date DATE NOT NULL,
    end_date DATE,
    renewal_date DATE, -- Next renewal date
    indexation_date DATE, -- Last indexation date
    indexation_rate DECIMAL(5, 2), -- Last used indexation rate (e.g., IRL)
    rent_payment_due_day INT DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active' -- e.g., 'active', 'expired', 'terminated'
);

-- Table: rent_payments (Suivi des Paiements de Loyers)
CREATE TABLE rent_payments (
    payment_id SERIAL PRIMARY KEY,
    rental_id INT REFERENCES rental_agreements(rental_id) ON DELETE CASCADE,
    payment_date DATE DEFAULT CURRENT_DATE,
    amount_paid DECIMAL(10, 2) NOT NULL,
    payment_type VARCHAR(50), -- e.g., 'bank_transfer', 'check', 'online'
    is_paid BOOLEAN DEFAULT FALSE,
    due_date DATE NOT NULL,
    receipt_url VARCHAR(255), -- Link to quittance de loyer PDF
    notes TEXT
);

-- Table: deposit_refunds (Gestion des Dépôts de Garantie)
CREATE TABLE deposit_refunds (
    refund_id SERIAL PRIMARY KEY,
    rental_id INT REFERENCES rental_agreements(rental_id) ON DELETE CASCADE,
    amount_refunded DECIMAL(10, 2) NOT NULL,
    refund_date DATE DEFAULT CURRENT_DATE,
    deductions TEXT, -- Reason for deductions as JSONB
    notes TEXT
);

-- Table: inventory_records (États des Lieux Numériques)
CREATE TABLE inventory_records (
    inventory_id SERIAL PRIMARY KEY,
    rental_id INT REFERENCES rental_agreements(rental_id) ON DELETE CASCADE,
    record_type VARCHAR(50) NOT NULL, -- 'entry' or 'exit'
    record_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    document_url VARCHAR(255), -- URL to PDF or digital record
    signed_by_tenant BOOLEAN DEFAULT FALSE,
    signed_by_agent BOOLEAN DEFAULT FALSE,
    notes TEXT,
    pictures_urls TEXT[] -- Array of picture URLs
);

-- Table: maintenance_requests (Maintenance & Réparations - Général et Locatif)
CREATE TABLE maintenance_requests (
    request_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    client_id INT REFERENCES clients(client_id), -- Tenant or owner reporting
    agent_id INT REFERENCES agents(agent_id), -- Agent managing the request
    description TEXT NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- e.g., 'pending', 'in_progress', 'completed', 'cancelled'
    priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    due_date DATE,
    resolution_date DATE,
    notes TEXT
);

-- Table: maintenance_interventions (Suivi des Interventions de Maintenance)
CREATE TABLE maintenance_interventions (
    intervention_id SERIAL PRIMARY KEY,
    request_id INT REFERENCES maintenance_requests(request_id) ON DELETE CASCADE,
    supplier_id INT REFERENCES suppliers(supplier_id),
    intervention_date DATE,
    description TEXT,
    cost DECIMAL(10, 2),
    status VARCHAR(50) DEFAULT 'scheduled', -- 'scheduled', 'completed', 'cancelled'
    invoice_url VARCHAR(255),
    photos_before_url TEXT[],
    photos_after_url TEXT[]
);

-- Table: suppliers (Gestion des Intervenants Externes - Fournisseurs/Artisans)
CREATE TABLE suppliers (
    supplier_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone_number VARCHAR(20),
    address TEXT,
    service_type VARCHAR(100), -- e.g., 'plumber', 'electrician', 'notary', 'diagnostics', 'bank'
    notes TEXT
);

-- Table: expenses (Gestion Financière - Dépenses de l'Agence)
CREATE TABLE expenses (
    expense_id SERIAL PRIMARY KEY,
    agency_id INT REFERENCES agencies(agency_id),
    agent_id INT REFERENCES agents(agent_id), -- If agent-specific expense
    description TEXT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    expense_date DATE DEFAULT CURRENT_DATE,
    category VARCHAR(50), -- e.g., 'marketing', 'office_supplies', 'travel', 'rent'
    receipt_url VARCHAR(255),
    is_reimbursable BOOLEAN DEFAULT FALSE
);

-- Table: invoices (Gestion Financière - Facturation)
CREATE TABLE invoices (
    invoice_id SERIAL PRIMARY KEY,
    transaction_id INT REFERENCES transactions(transaction_id),
    client_id INT REFERENCES clients(client_id), -- Client being invoiced
    agent_id INT REFERENCES agents(agent_id),
    invoice_date DATE DEFAULT CURRENT_DATE,
    due_date DATE,
    total_amount DECIMAL(15, 2) NOT NULL,
    amount_paid DECIMAL(15, 2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'paid', 'overdue', 'cancelled'
    invoice_type VARCHAR(50) NOT NULL, -- e.g., 'honorary', 'rental_fee', 'commission_deduction'
    invoice_url VARCHAR(255)
);

-- Table: payments (Suivi des Encaissements et Rapprochement Bancaire)
CREATE TABLE payments (
    payment_id SERIAL PRIMARY KEY,
    invoice_id INT REFERENCES invoices(invoice_id),
    transaction_id INT REFERENCES transactions(transaction_id), -- For payments not directly tied to an invoice (e.g., initial deposit)
    client_id INT REFERENCES clients(client_id), -- Payer
    agent_id INT REFERENCES agents(agent_id), -- Agent involved in receiving payment
    amount DECIMAL(15, 2) NOT NULL,
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_method VARCHAR(50), -- e.g., 'bank_transfer', 'credit_card', 'check', 'cash', 'mobile_money'
    bank_account_id INT REFERENCES bank_accounts(account_id), -- For reconciliation
    notes TEXT
);

-- Table: bank_accounts (Intégration Bancaire)
CREATE TABLE bank_accounts (
    account_id SERIAL PRIMARY KEY,
    agency_id INT REFERENCES agencies(agency_id),
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) UNIQUE NOT NULL,
    account_type VARCHAR(50), -- e.g., 'checking', 'savings', 'escrow'
    balance DECIMAL(15, 2) DEFAULT 0.00,
    currency VARCHAR(10) DEFAULT 'XOF' -- Default to West African CFA franc
);

-- Table: communication_logs (Communication Client)
CREATE TABLE communication_logs (
    log_id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(client_id),
    agent_id INT REFERENCES agents(agent_id),
    log_type VARCHAR(50) NOT NULL, -- e.g., 'email', 'sms', 'call', 'whatsapp', 'meeting'
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    subject VARCHAR(255),
    content TEXT,
    direction VARCHAR(10) -- 'incoming' or 'outgoing'
);

-- Table: marketing_campaigns (Marketing & Diffusion)
CREATE TABLE marketing_campaigns (
    campaign_id SERIAL PRIMARY KEY,
    campaign_name VARCHAR(255) NOT NULL,
    campaign_type VARCHAR(50) NOT NULL, -- e.g., 'email', 'sms', 'social_media', 'print', 'event'
    start_date DATE,
    end_date DATE,
    budget DECIMAL(10, 2),
    target_audience TEXT,
    status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'active', 'completed'
    notes TEXT
);

-- Table: campaign_performance (Suivi des performances marketing)
CREATE TABLE campaign_performance (
    performance_id SERIAL PRIMARY KEY,
    campaign_id INT REFERENCES marketing_campaigns(campaign_id) ON DELETE CASCADE,
    metric_date DATE DEFAULT CURRENT_DATE,
    impressions INT DEFAULT 0,
    clicks INT DEFAULT 0,
    conversions INT DEFAULT 0,
    cost DECIMAL(10, 2) DEFAULT 0.00,
    notes TEXT
);

-- Table: property_views (Suivi des vues d'annonces)
CREATE TABLE property_views (
    view_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id) ON DELETE CASCADE,
    client_id INT REFERENCES clients(client_id), -- If logged in client
    view_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45), -- For anonymous views
    user_agent TEXT,
    source_platform VARCHAR(50) -- e.g., 'website', 'seloger', 'leboncoin'
);

-- Table: user_activity_logs (Administration Système & Sécurité - Audit Trail)
CREATE TABLE user_activity_logs (
    log_id SERIAL PRIMARY KEY,
    agent_id INT REFERENCES agents(agent_id),
    action_type VARCHAR(100) NOT NULL, -- e.g., 'property_created', 'client_updated', 'document_signed'
    entity_type VARCHAR(50), -- e.g., 'property', 'client', 'mandate'
    entity_id INT, -- ID of the entity affected
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details JSONB, -- Store old/new values or specific details
    ip_address VARCHAR(45)
);

-- Table: faqs (Support & Administration Système - Centre d'aide)
CREATE TABLE faqs (
    faq_id SERIAL PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(100),
    is_published BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: tutorials (Support & Administration Système - Centre d'aide)
CREATE TABLE tutorials (
    tutorial_id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    video_url VARCHAR(255),
    category VARCHAR(100),
    is_published BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table: tickets (Support Client Intégré)
CREATE TABLE tickets (
    ticket_id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(client_id), -- If client reported
    agent_id INT REFERENCES agents(agent_id), -- Agent assigned
    subject VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'open', -- 'open', 'in_progress', 'resolved', 'closed'
    priority VARCHAR(20) DEFAULT 'medium',
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolution_date TIMESTAMP
);

-- Table: network_agencies (Gestion Multi-Agences & Réseaux - Partenariats)
CREATE TABLE network_agencies (
    network_agency_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address TEXT,
    contact_email VARCHAR(100),
    phone_number VARCHAR(20),
    website VARCHAR(255),
    partnership_type VARCHAR(50) -- e.g., 'co_exclusive', 'referral'
);

-- Table: shared_properties (Partage de biens inter-agences)
CREATE TABLE shared_properties (
    shared_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    sharing_agency_id INT REFERENCES agencies(agency_id),
    receiving_agency_id INT REFERENCES network_agencies(network_agency_id),
    share_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    terms TEXT -- Details of sharing terms, commission split
);

-- Table: reviews (Avis Clients)
CREATE TABLE reviews (
    review_id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(client_id),
    transaction_id INT REFERENCES transactions(transaction_id),
    rating INT CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_moderated BOOLEAN DEFAULT FALSE,
    moderation_notes TEXT
);

-- Table: property_comparisons (Historique des comparaisons de biens)
CREATE TABLE property_comparisons (
    comparison_id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(client_id),
    comparison_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    properties_compared INT[] NOT NULL -- Array of property_ids
);

-- Table: expenses_reimbursement (Gestion des Notes de Frais)
CREATE TABLE expenses_reimbursement (
    reimbursement_id SERIAL PRIMARY KEY,
    agent_id INT REFERENCES agents(agent_id),
    expense_date DATE NOT NULL,
    description TEXT,
    amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'paid'
    receipt_url VARCHAR(255),
    approval_date DATE,
    approver_agent_id INT REFERENCES agents(agent_id)
);

-- Table: vacation_requests (Gestion des Congés)
CREATE TABLE vacation_requests (
    request_id SERIAL PRIMARY KEY,
    agent_id INT REFERENCES agents(agent_id),
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
    approver_agent_id INT REFERENCES agents(agent_id),
    approval_date TIMESTAMP,
    notes TEXT
);

-- Table: property_energies (Suivi des Consommations Énergétiques)
CREATE TABLE property_energies (
    energy_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    reading_date DATE NOT NULL,
    meter_type VARCHAR(50) NOT NULL, -- e.g., 'electricity', 'gas', 'water'
    consumption_unit VARCHAR(20), -- e.g., 'kWh', 'm3'
    consumption_value DECIMAL(10, 2) NOT NULL,
    cost DECIMAL(10, 2),
    notes TEXT
);

-- Table: crm_preferences (Paramètres de personnalisation du CRM)
CREATE TABLE crm_preferences (
    pref_id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(client_id),
    property_type_pref TEXT[], -- Array of preferred property types
    min_price DECIMAL(15, 2),
    max_price DECIMAL(15, 2),
    preferred_locations TEXT[], -- Array of preferred cities/neighborhoods
    number_of_rooms_pref INT[], -- Array of preferred number of rooms
    notes TEXT
);

-- Table: portal_sync_logs (Logs de synchronisation avec les portails immobiliers)
CREATE TABLE portal_sync_logs (
    log_id SERIAL PRIMARY KEY,
    property_id INT REFERENCES properties(property_id),
    portal_name VARCHAR(100) NOT NULL,
    sync_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) NOT NULL, -- 'success', 'failed', 'partial'
    error_message TEXT
);

-- Table: notifications (Notifications push et SMS)
CREATE TABLE notifications (
    notification_id SERIAL PRIMARY KEY,
    recipient_type VARCHAR(50) NOT NULL, -- 'agent', 'client'
    recipient_id INT NOT NULL, -- agent_id or client_id
    message TEXT NOT NULL,
    notification_type VARCHAR(50), -- e.g., 'new_lead', 'appointment_reminder', 'document_signed', 'new_property_match'
    is_read BOOLEAN DEFAULT FALSE,
    sent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivery_status VARCHAR(50) -- e.g., 'sent', 'delivered', 'failed'
);

-- Table: property_alerts (Alertes clients sur biens)
CREATE TABLE property_alerts (
    alert_id SERIAL PRIMARY KEY,
    client_id INT REFERENCES clients(client_id) ON DELETE CASCADE,
    criteria JSONB NOT NULL, -- JSONB of search criteria for the alert
    creation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sent_date TIMESTAMP,
    frequency VARCHAR(50) DEFAULT 'daily', -- 'daily', 'weekly', 'instant'
    is_active BOOLEAN DEFAULT TRUE
);

-- =====================
-- 2. Views (Vues)
-- =====================
-- View: vw_agent_performance (Rapports de performance des agents)
CREATE OR REPLACE VIEW vw_agent_performance AS
SELECT
    a.agent_id,
    a.first_name,
    a.last_name,
    COUNT(DISTINCT t.transaction_id) AS total_transactions,
    SUM(t.commission_amount) AS total_commissions_earned,
    (COUNT(DISTINCT t.transaction_id) FILTER (WHERE t.transaction_type = 'sale')) AS total_sales,
    (COUNT(DISTINCT t.transaction_id) FILTER (WHERE t.transaction_type = 'rental')) AS total_rentals,
    COUNT(DISTINCT ap.appointment_id) AS total_visits_scheduled,
    (SELECT COUNT(DISTINCT offers.offer_id) FROM offers WHERE offers.agent_id = a.agent_id AND offers.status = 'accepted') AS accepted_offers,
    (SELECT COUNT(DISTINCT clients.client_id) FROM clients WHERE clients.fk_agent_id = a.agent_id) AS managed_clients
FROM
    agents a
LEFT JOIN
    transactions t ON a.agent_id = t.agent_id
LEFT JOIN
    appointments ap ON a.agent_id = ap.agent_id
GROUP BY
    a.agent_id, a.first_name, a.last_name;

-- View: vw_property_overview (Vue d'ensemble des biens avec leurs propriétaires)
CREATE OR REPLACE VIEW vw_property_overview AS
SELECT
    p.property_id,
    p.address,
    p.city,
    p.price,
    p.status,
    p.transaction_type,
    p.property_type,
    p.area_sqm,
    p.number_of_rooms,
    p.description,
    p.last_update,
    jsonb_build_object(
        'owner_id', c.client_id,
        'first_name', c.first_name,
        'last_name', c.last_name,
        'email', c.email,
        'phone_number', c.phone_number
    ) AS owner_details,
    (SELECT media_url FROM property_media WHERE property_id = p.property_id AND media_type = 'photo' ORDER BY upload_date DESC LIMIT 1) AS primary_photo_url
FROM
    properties p
LEFT JOIN
    clients c ON p.fk_owner_client_id = c.client_id;

-- View: vw_client_matching_properties (Biens pertinents pour chaque client)
CREATE OR REPLACE VIEW vw_client_matching_properties AS
SELECT
    c.client_id,
    c.first_name AS client_first_name,
    c.last_name AS client_last_name,
    p.property_id,
    p.address,
    p.city,
    p.price,
    p.property_type,
    p.area_sqm,
    p.number_of_rooms
FROM
    clients c
JOIN
    properties p ON
        (p.transaction_type = 'sale' AND c.client_type = 'acquirer' AND p.price BETWEEN (c.preferences->>'min_budget')::NUMERIC AND (c.preferences->>'max_budget')::NUMERIC AND p.property_type = ANY(ARRAY(SELECT jsonb_array_elements_text(c.preferences->'preferred_property_types'))))
        OR
        (p.transaction_type = 'rent' AND c.client_type = 'tenant' AND p.price BETWEEN (c.preferences->>'min_rent')::NUMERIC AND (c.preferences->>'max_rent')::NUMERIC AND p.property_type = ANY(ARRAY(SELECT jsonb_array_elements_text(c.preferences->'preferred_property_types'))))
WHERE
    p.status = 'available';

-- View: vw_overdue_rent_payments (Paiements de loyers en retard)
CREATE OR REPLACE VIEW vw_overdue_rent_payments AS
SELECT
    rp.payment_id,
    rp.rental_id,
    p.address AS property_address,
    t.first_name AS tenant_first_name,
    t.last_name AS tenant_last_name,
    rp.amount_paid,
    rp.due_date,
    CURRENT_DATE - rp.due_date AS days_overdue
FROM
    rent_payments rp
JOIN
    rental_agreements ra ON rp.rental_id = ra.rental_id
JOIN
    properties p ON ra.property_id = p.property_id
JOIN
    clients t ON ra.tenant_client_id = t.client_id
WHERE
    rp.is_paid = FALSE AND rp.due_date < CURRENT_DATE;

-- View: vw_mandates_expiring_soon (Mandats expirant bientôt)
CREATE OR REPLACE VIEW vw_mandates_expiring_soon AS
SELECT
    m.mandate_id,
    p.address,
    c.first_name AS owner_first_name,
    c.last_name AS owner_last_name,
    m.mandate_type,
    m.end_date,
    m.agent_id
FROM
    mandates m
JOIN
    properties p ON m.property_id = p.property_id
JOIN
    clients c ON m.client_id = c.client_id
WHERE
    m.mandate_status = 'active' AND m.end_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days';

-- =====================
-- 3. Functions (Fonctions)
-- =====================
-- Function: calculate_agent_commission (Calcul de commission pour un agent sur une transaction)
CREATE OR REPLACE FUNCTION calculate_agent_commission(
    transaction_id INT
)
RETURNS DECIMAL(15, 2) AS $$
DECLARE
    final_price DECIMAL(15, 2);
    agent_rate DECIMAL(5, 2);
    commission DECIMAL(15, 2);
BEGIN
    SELECT t.final_price, a.commission_rate
    INTO final_price, agent_rate
    FROM transactions t
    JOIN agents a ON t.agent_id = a.agent_id
    WHERE t.transaction_id = calculate_agent_commission.transaction_id;

    IF final_price IS NULL OR agent_rate IS NULL THEN
        RETURN 0.00;
    END IF;

    commission := final_price * (agent_rate / 100);
    RETURN commission;
END;
$$ LANGUAGE plpgsql;

-- Function: get_property_estimated_value (Estimation de la valeur d'un bien - Simplifié pour le schéma)
-- In a real-world scenario, this would integrate with an external AI/ML model or complex local market data.
CREATE OR REPLACE FUNCTION get_property_estimated_value(
    property_id INT
)
RETURNS DECIMAL(15, 2) AS $$
DECLARE
    prop_type VARCHAR(50);
    prop_area DECIMAL(10, 2);
    prop_rooms INT;
    city_name VARCHAR(100);
    estimated_value DECIMAL(15, 2);
BEGIN
    SELECT property_type, area_sqm, number_of_rooms, city
    INTO prop_type, prop_area, prop_rooms, city_name
    FROM properties
    WHERE properties.property_id = get_property_estimated_value.property_id;

    -- Basic estimation logic for demonstration
    -- Replace with actual AI/ML integration for real-world use
    estimated_value := prop_area * 3000; -- Base price per sqm
    IF prop_type = 'house' THEN
        estimated_value := estimated_value * 1.2;
    ELSIF prop_type = 'apartment' THEN
        estimated_value := estimated_value * 0.9;
    END IF;

    -- Add a small factor for number of rooms
    estimated_value := estimated_value + (prop_rooms * 5000);

    -- Simulate city-specific adjustments (e.g., higher for Cotonou)
    IF city_name = 'Cotonou' THEN
        estimated_value := estimated_value * 1.5;
    ELSIF city_name = 'Porto-Novo' THEN
        estimated_value := estimated_value * 1.1;
    END IF;

    RETURN estimated_value;
END;
$$ LANGUAGE plpgsql;

-- Function: get_available_appointment_slots (Slots disponibles pour RDV)
CREATE OR REPLACE FUNCTION get_available_appointment_slots(
    target_date DATE,
    target_agent_id INT DEFAULT NULL,
    target_property_id INT DEFAULT NULL
)
RETURNS TABLE (slot_start_time TIMESTAMP, slot_end_time TIMESTAMP) AS $$
BEGIN
    RETURN QUERY
    SELECT
        generate_series(
            target_date + INTERVAL '9 hours',
            target_date + INTERVAL '18 hours' - INTERVAL '30 minutes',
            INTERVAL '30 minutes'
        ) AS slot_start_time,
        generate_series(
            target_date + INTERVAL '9 hours' + INTERVAL '30 minutes',
            target_date + INTERVAL '18 hours',
            INTERVAL '30 minutes'
        ) AS slot_end_time
    WHERE NOT EXISTS (
        SELECT 1
        FROM appointments
        WHERE
            (agent_id = target_agent_id OR target_agent_id IS NULL) AND
            (property_id = target_property_id OR target_property_id IS NULL) AND
            (
                (start_time, end_time) OVERLAPS (generate_series(target_date + INTERVAL '9 hours', target_date + INTERVAL '18 hours' - INTERVAL '30 minutes', INTERVAL '30 minutes'),
                                                generate_series(target_date + INTERVAL '9 hours' + INTERVAL '30 minutes', target_date + INTERVAL '18 hours', INTERVAL '30 minutes'))
            )
    );
END;
$$ LANGUAGE plpgsql;

-- Function: update_lead_score (Mise à jour du score de lead client)
CREATE OR REPLACE FUNCTION update_lead_score(p_client_id INT)
RETURNS VOID AS $$
DECLARE
    interaction_count INT;
    visit_count INT;
    offer_count INT;
    new_score DECIMAL(5, 2);
BEGIN
    SELECT COUNT(*) INTO interaction_count FROM communication_logs WHERE client_id = p_client_id;
    SELECT COUNT(*) INTO visit_count FROM appointments WHERE client_id = p_client_id AND appointment_type = 'visit' AND status = 'completed';
    SELECT COUNT(*) INTO offer_count FROM offers WHERE client_id = p_client_id AND status IN ('pending', 'accepted', 'countered');

    -- Simple scoring logic: more interactions, visits, offers = higher score
    new_score := (interaction_count * 0.1) + (visit_count * 0.5) + (offer_count * 1.0);
    new_score := LEAST(new_score, 100.00); -- Cap score at 100

    UPDATE clients
    SET lead_score = new_score, last_interaction = CURRENT_TIMESTAMP
    WHERE client_id = p_client_id;
END;
$$ LANGUAGE plpgsql;

-- =====================
-- 4. Procedures (Procédures)
-- =====================
-- Procedure: create_new_transaction (Création d'une nouvelle transaction)
CREATE OR REPLACE PROCEDURE create_new_transaction(
    p_property_id INT,
    p_buyer_client_id INT,
    p_seller_client_id INT,
    p_agent_id INT,
    p_transaction_type VARCHAR(50),
    p_final_price DECIMAL(15, 2),
    OUT p_transaction_id INT
)
LANGUAGE plpgsql AS $$
DECLARE
    v_commission_amount DECIMAL(15, 2);
BEGIN
    -- Calculate commission for the agent
    v_commission_amount := p_final_price * (SELECT commission_rate / 100 FROM agents WHERE agent_id = p_agent_id);

    INSERT INTO transactions (
        property_id, buyer_client_id, seller_client_id, agent_id,
        transaction_type, final_price, commission_amount
    )
    VALUES (
        p_property_id, p_buyer_client_id, p_seller_client_id, p_agent_id,
        p_transaction_type, p_final_price, v_commission_amount
    )
    RETURNING transaction_id INTO p_transaction_id;

    -- Update property status
    UPDATE properties
    SET status = CASE
        WHEN p_transaction_type = 'sale' THEN 'under_offer' -- Or 'sold' if directly closing
        WHEN p_transaction_type = 'rental' THEN 'reserved' -- Or 'rented' if directly closing
        ELSE status
    END
    WHERE property_id = p_property_id;

    -- Insert initial transaction steps
    IF p_transaction_type = 'sale' THEN
        INSERT INTO transaction_steps (transaction_id, step_name, status) VALUES
        (p_transaction_id, 'Offer Accepted', 'completed'),
        (p_transaction_id, 'Compromise Signed', 'pending'),
        (p_transaction_id, 'Financing Obtained', 'pending'),
        (p_transaction_id, 'Notary Act', 'pending'),
        (p_transaction_id, 'Keys Handed Over', 'pending');
    ELSIF p_transaction_type = 'rental' THEN
        INSERT INTO transaction_steps (transaction_id, step_name, status) VALUES
        (p_transaction_id, 'Offer Accepted', 'completed'),
        (p_transaction_id, 'Lease Agreement Signed', 'pending'),
        (p_transaction_id, 'Deposit Paid', 'pending'),
        (p_transaction_id, 'First Rent Paid', 'pending'),
        (p_transaction_id, 'Inventory Entry', 'pending'),
        (p_transaction_id, 'Keys Handed Over', 'pending');
    END IF;

    -- Log the transaction creation
    INSERT INTO user_activity_logs (agent_id, action_type, entity_type, entity_id, details)
    VALUES (p_agent_id, 'transaction_created', 'transaction', p_transaction_id,
            jsonb_build_object('property_id', p_property_id, 'buyer_id', p_buyer_client_id, 'seller_id', p_seller_client_id));

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error creating transaction: %', SQLERRM;
END;
$$;

-- Procedure: process_rental_payment (Traitement d'un paiement de loyer)
CREATE OR REPLACE PROCEDURE process_rental_payment(
    p_rental_id INT,
    p_amount_paid DECIMAL(10, 2),
    p_payment_method VARCHAR(50),
    p_bank_account_id INT DEFAULT NULL
)
LANGUAGE plpgsql AS $$
DECLARE
    v_total_due DECIMAL(10, 2);
    v_client_id INT;
    v_agent_id INT;
BEGIN
    SELECT (rent_amount + charges_amount), tenant_client_id, agent_id
    INTO v_total_due, v_client_id, v_agent_id
    FROM rental_agreements ra
    JOIN properties p ON ra.property_id = p.property_id
    WHERE rental_id = p_rental_id;

    IF v_total_due IS NULL THEN
        RAISE EXCEPTION 'Rental agreement not found.';
    END IF;

    INSERT INTO rent_payments (rental_id, amount_paid, payment_method, is_paid, due_date)
    VALUES (p_rental_id, p_amount_paid, p_payment_method, TRUE, CURRENT_DATE); -- Assuming payment is for current due date

    -- Update invoice status if applicable (assuming invoices are generated for rents)
    UPDATE invoices
    SET amount_paid = amount_paid + p_amount_paid,
        status = CASE WHEN total_amount <= (amount_paid + p_amount_paid) THEN 'paid' ELSE 'pending' END
    WHERE client_id = v_client_id AND invoice_type = 'rental_fee' AND status != 'paid'
    ORDER BY invoice_date DESC LIMIT 1; -- Update the most recent rental invoice

    -- Log the payment
    INSERT INTO user_activity_logs (agent_id, action_type, entity_type, entity_id, details)
    VALUES (v_agent_id, 'rental_payment_processed', 'rental_agreement', p_rental_id,
            jsonb_build_object('amount', p_amount_paid, 'method', p_payment_method));

    -- Potentially send a notification to the tenant/landlord
    INSERT INTO notifications (recipient_type, recipient_id, message, notification_type)
    VALUES ('client', v_client_id, 'Your rent payment of ' || p_amount_paid || ' XOF has been received.', 'rent_payment_confirmation');

END;
$$;

-- =====================
-- 5. Triggers (Déclencheurs)
-- =====================
-- Trigger: trg_update_property_last_update (Mise à jour automatique de la date de dernière modification d'un bien)
CREATE OR REPLACE FUNCTION set_property_last_update()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_update = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_property_last_update
BEFORE UPDATE ON properties
FOR EACH ROW
EXECUTE FUNCTION set_property_last_update();

-- Trigger: trg_log_user_activity (Journalisation des activités des utilisateurs)
CREATE OR REPLACE FUNCTION log_user_activity()
RETURNS TRIGGER AS $$
DECLARE
    v_details JSONB;
    v_action_type VARCHAR(100);
BEGIN
    IF TG_OP = 'INSERT' THEN
        v_action_type := TG_TABLE_NAME || '_created';
        v_details := jsonb_build_object('new_record', to_jsonb(NEW));
    ELSIF TG_OP = 'UPDATE' THEN
        v_action_type := TG_TABLE_NAME || '_updated';
        v_details := jsonb_build_object('old_record', to_jsonb(OLD), 'new_record', to_jsonb(NEW));
    ELSIF TG_OP = 'DELETE' THEN
        v_action_type := TG_TABLE_NAME || '_deleted';
        v_details := jsonb_build_object('deleted_record', to_jsonb(OLD));
    END IF;

    -- This assumes there's a way to get the current agent_id from the application context.
    -- For web applications, this might be a session variable or passed through a connection setting.
    -- For simplicity, we'll assume a dummy agent_id or NULL if not available.
    INSERT INTO user_activity_logs (agent_id, action_type, entity_type, entity_id, details, ip_address)
    VALUES (
        NULL, -- Replace with actual agent_id from session/context
        v_action_type,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id, NEW.property_id, OLD.property_id, NEW.client_id, OLD.client_id), -- Try to get relevant ID
        v_details,
        '0.0.0.0' -- Replace with actual IP address from connection
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply this trigger to relevant tables (example for properties and clients)
CREATE TRIGGER trg_properties_activity_log
AFTER INSERT OR UPDATE OR DELETE ON properties
FOR EACH ROW
EXECUTE FUNCTION log_user_activity();

CREATE TRIGGER trg_clients_activity_log
AFTER INSERT OR UPDATE OR DELETE ON clients
FOR EACH ROW
EXECUTE FUNCTION log_user_activity();

-- Trigger: trg_update_client_last_interaction (Mise à jour de la date de dernière interaction client)
CREATE OR REPLACE FUNCTION update_client_last_interaction_func()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE clients
    SET last_interaction = CURRENT_TIMESTAMP
    WHERE client_id = NEW.client_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_client_last_interaction
AFTER INSERT OR UPDATE ON communication_logs
FOR EACH ROW
EXECUTE FUNCTION update_client_last_interaction_func();

-- Trigger: trg_mandate_expiration_alert (Notification automatique avant l'expiration du mandat)
-- This trigger would likely be less performant for large datasets and is better handled by a background job
-- but included for illustrative purposes for the 'alert' functionality.
CREATE OR REPLACE FUNCTION mandate_expiration_alert_func()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.end_date <= CURRENT_DATE + INTERVAL '7 days' AND NEW.mandate_status = 'active' THEN
        INSERT INTO notifications (recipient_type, recipient_id, message, notification_type)
        VALUES ('agent', NEW.agent_id, 'Mandate for property ' || NEW.property_id || ' is expiring soon (' || NEW.end_date || ').', 'mandate_expiration');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_mandate_expiration_alert
AFTER UPDATE ON mandates
FOR EACH ROW
WHEN (OLD.end_date IS DISTINCT FROM NEW.end_date OR OLD.mandate_status IS DISTINCT FROM NEW.mandate_status)
EXECUTE FUNCTION mandate_expiration_alert_func();
