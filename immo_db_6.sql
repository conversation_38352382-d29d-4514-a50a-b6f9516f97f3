-- ==================================================
-- SCHÉMA SQL POSTGRESQL COMPLET POUR GESTION AGENCE IMMOBILIÈRE
-- ==================================================

-- Extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "hstore";

-- ==================================================
-- 1. TABLES DE RÉFÉRENCE ET CONFIGURATION
-- ==================================================

-- Types d'entités
CREATE TYPE entity_type AS ENUM ('agency', 'individual', 'company', 'government');
CREATE TYPE property_type AS ENUM ('apartment', 'house', 'land', 'commercial', 'office', 'warehouse', 'parking', 'other');
CREATE TYPE transaction_type AS ENUM ('sale', 'rental', 'seasonal_rental', 'management');
CREATE TYPE property_status AS ENUM ('available', 'under_offer', 'reserved', 'sold', 'rented', 'withdrawn', 'suspended');
CREATE TYPE mandate_type AS ENUM ('simple', 'exclusive', 'semi_exclusive');
CREATE TYPE contact_method AS ENUM ('email', 'phone', 'sms', 'whatsapp', 'postal');
CREATE TYPE user_role AS ENUM ('admin', 'manager', 'agent', 'assistant', 'accountant', 'viewer');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'overdue', 'cancelled', 'refunded');
CREATE TYPE document_type AS ENUM ('contract', 'mandate', 'diagnosis', 'photo', 'video', 'plan', 'identity', 'financial', 'legal', 'other');
CREATE TYPE intervention_status AS ENUM ('requested', 'scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE lead_source AS ENUM ('website', 'portal', 'social_media', 'referral', 'phone', 'walk_in', 'advertising', 'other');
CREATE TYPE priority_level AS ENUM ('low', 'medium', 'high', 'urgent');

-- Table des agences
CREATE TABLE agencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    legal_name VARCHAR(255),
    siret VARCHAR(14),
    license_number VARCHAR(50),
    logo_url TEXT,
    website VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    address JSONB,
    coordinates GEOGRAPHY(POINT, 4326),
    settings JSONB DEFAULT '{}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Table des utilisateurs
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    mobile VARCHAR(20),
    role user_role NOT NULL DEFAULT 'agent',
    permissions JSONB DEFAULT '{}'::jsonb,
    profile_picture_url TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 2. GESTION DES CLIENTS ET CONTACTS
-- ==================================================

-- Table des contacts (clients, prospects, propriétaires)
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    type entity_type NOT NULL DEFAULT 'individual',
    title VARCHAR(10), -- M., Mme, Dr, etc.
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    company_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    address JSONB,
    coordinates GEOGRAPHY(POINT, 4326),
    birth_date DATE,
    profession VARCHAR(100),
    income_range VARCHAR(50),
    family_situation VARCHAR(50),
    preferences JSONB DEFAULT '{}'::jsonb,
    budget_min DECIMAL(12,2),
    budget_max DECIMAL(12,2),
    notes TEXT,
    tags TEXT[],
    lead_source lead_source,
    assigned_agent_id UUID REFERENCES users(id),
    score INTEGER DEFAULT 0,
    is_blacklisted BOOLEAN DEFAULT false,
    gdpr_consent BOOLEAN DEFAULT false,
    gdpr_consent_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Historique des interactions avec les contacts
CREATE TABLE contact_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    type contact_method NOT NULL,
    subject VARCHAR(255),
    content TEXT,
    duration_minutes INTEGER,
    scheduled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 3. GESTION DES BIENS IMMOBILIERS
-- ==================================================

-- Table des propriétés
CREATE TABLE properties (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    reference VARCHAR(50) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type property_type NOT NULL,
    transaction_type transaction_type NOT NULL,
    status property_status DEFAULT 'available',
    
    -- Localisation
    address JSONB NOT NULL,
    coordinates GEOGRAPHY(POINT, 4326),
    floor INTEGER,
    floor_total INTEGER,
    
    -- Caractéristiques
    surface_living DECIMAL(8,2),
    surface_total DECIMAL(8,2),
    surface_land DECIMAL(10,2),
    rooms_total INTEGER,
    bedrooms INTEGER,
    bathrooms INTEGER,
    toilets INTEGER,
    parking_spaces INTEGER,
    balconies INTEGER,
    terraces INTEGER,
    
    -- Caractéristiques techniques
    construction_year INTEGER,
    renovation_year INTEGER,
    energy_class VARCHAR(1),
    ges_class VARCHAR(1),
    heating_type VARCHAR(50),
    heating_energy VARCHAR(50),
    
    -- Financier
    price DECIMAL(12,2),
    price_per_sqm DECIMAL(8,2),
    charges DECIMAL(8,2),
    property_tax DECIMAL(8,2),
    fees_percentage DECIMAL(5,2),
    fees_amount DECIMAL(10,2),
    
    -- Équipements et commodités
    amenities TEXT[],
    equipment JSONB DEFAULT '{}'::jsonb,
    
    -- Copropriété
    condominium_charges DECIMAL(8,2),
    condominium_lots_total INTEGER,
    
    -- Métadonnées
    owner_id UUID REFERENCES contacts(id),
    assigned_agent_id UUID REFERENCES users(id),
    visit_count INTEGER DEFAULT 0,
    favorite_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    
    is_published BOOLEAN DEFAULT false,
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Médias associés aux propriétés
CREATE TABLE property_media (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    type VARCHAR(20) NOT NULL, -- 'photo', 'video', 'virtual_tour', 'plan'
    url TEXT NOT NULL,
    title VARCHAR(255),
    description TEXT,
    is_main BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 4. GESTION DES MANDATS
-- ==================================================

CREATE TABLE mandates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    type mandate_type NOT NULL,
    transaction_type transaction_type NOT NULL,
    
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    auto_renewal BOOLEAN DEFAULT false,
    renewal_duration INTERVAL DEFAULT '3 months',
    
    commission_rate DECIMAL(5,2),
    commission_amount DECIMAL(10,2),
    exclusive_territory GEOMETRY,
    
    signed_at TIMESTAMP WITH TIME ZONE,
    signed_by_owner BOOLEAN DEFAULT false,
    signed_by_agent UUID REFERENCES users(id),
    
    status VARCHAR(20) DEFAULT 'active', -- active, expired, terminated, renewed
    termination_reason TEXT,
    
    terms_conditions TEXT,
    special_clauses TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 5. GESTION DES VISITES
-- ==================================================

CREATE TABLE appointments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES users(id),
    
    type VARCHAR(50) NOT NULL, -- 'visit', 'valuation', 'signing', 'meeting'
    title VARCHAR(255),
    description TEXT,
    
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    status VARCHAR(20) DEFAULT 'scheduled', -- scheduled, confirmed, completed, cancelled, no_show
    
    location_type VARCHAR(20) DEFAULT 'property', -- property, office, remote
    meeting_location JSONB,
    
    attendees JSONB DEFAULT '[]'::jsonb,
    notes TEXT,
    feedback JSONB DEFAULT '{}'::jsonb,
    
    reminder_sent BOOLEAN DEFAULT false,
    confirmation_sent BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Feedback des visites
CREATE TABLE visit_feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    appointment_id UUID REFERENCES appointments(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id),
    
    interest_level INTEGER CHECK (interest_level BETWEEN 1 AND 5),
    price_feedback VARCHAR(50), -- 'too_expensive', 'fair', 'good_value'
    property_condition VARCHAR(50),
    location_rating INTEGER CHECK (location_rating BETWEEN 1 AND 5),
    
    positive_points TEXT[],
    negative_points TEXT[],
    additional_comments TEXT,
    
    will_make_offer BOOLEAN,
    estimated_offer_amount DECIMAL(12,2),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 6. GESTION DES TRANSACTIONS
-- ==================================================

CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    buyer_id UUID REFERENCES contacts(id),
    seller_id UUID REFERENCES contacts(id),
    agent_id UUID REFERENCES users(id),
    
    type transaction_type NOT NULL,
    status VARCHAR(50) DEFAULT 'initiated',
    
    offer_amount DECIMAL(12,2),
    final_amount DECIMAL(12,2),
    deposit_amount DECIMAL(12,2),
    
    offer_date DATE,
    acceptance_date DATE,
    signing_date DATE,
    completion_date DATE,
    
    commission_rate DECIMAL(5,2),
    commission_amount DECIMAL(10,2),
    
    conditions_precedent TEXT[],
    financing_approved BOOLEAN DEFAULT false,
    financing_amount DECIMAL(12,2),
    financing_rate DECIMAL(5,4),
    
    notary_id UUID REFERENCES contacts(id),
    notary_fees DECIMAL(10,2),
    
    notes TEXT,
    documents JSONB DEFAULT '[]'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 7. GESTION LOCATIVE
-- ==================================================

-- Contrats de location
CREATE TABLE rental_contracts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    landlord_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES users(id),
    
    contract_number VARCHAR(50) UNIQUE,
    type VARCHAR(50) DEFAULT 'residential', -- residential, commercial, seasonal
    
    start_date DATE NOT NULL,
    end_date DATE,
    notice_period_months INTEGER DEFAULT 1,
    automatic_renewal BOOLEAN DEFAULT true,
    
    monthly_rent DECIMAL(10,2) NOT NULL,
    charges DECIMAL(10,2) DEFAULT 0,
    security_deposit DECIMAL(10,2),
    
    rent_revision_date DATE,
    rent_revision_index VARCHAR(10) DEFAULT 'IRL',
    
    special_clauses TEXT,
    inventory_in JSONB,
    inventory_out JSONB,
    
    status VARCHAR(20) DEFAULT 'active', -- active, terminated, suspended
    termination_date DATE,
    termination_reason TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Paiements de loyer
CREATE TABLE rent_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rental_contract_id UUID REFERENCES rental_contracts(id) ON DELETE CASCADE,
    
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    due_date DATE NOT NULL,
    
    rent_amount DECIMAL(10,2) NOT NULL,
    charges_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    
    paid_amount DECIMAL(10,2) DEFAULT 0,
    payment_date DATE,
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    
    status payment_status DEFAULT 'pending',
    late_fees DECIMAL(8,2) DEFAULT 0,
    
    receipt_generated BOOLEAN DEFAULT false,
    receipt_sent BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Interventions et maintenance
CREATE TABLE maintenance_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    rental_contract_id UUID REFERENCES rental_contracts(id),
    requested_by UUID REFERENCES contacts(id),
    assigned_to UUID REFERENCES users(id),
    
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50), -- 'plumbing', 'electrical', 'heating', 'general'
    priority priority_level DEFAULT 'medium',
    
    status intervention_status DEFAULT 'requested',
    
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    
    scheduled_date TIMESTAMP WITH TIME ZONE,
    completed_date TIMESTAMP WITH TIME ZONE,
    
    service_provider_id UUID REFERENCES contacts(id),
    
    photos TEXT[],
    documents TEXT[],
    notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 8. GESTION DOCUMENTAIRE
-- ==================================================

CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    
    title VARCHAR(255) NOT NULL,
    description TEXT,
    type document_type NOT NULL,
    category VARCHAR(100),
    
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    file_hash VARCHAR(64), -- SHA-256
    
    -- Relations
    property_id UUID REFERENCES properties(id),
    contact_id UUID REFERENCES contacts(id),
    transaction_id UUID REFERENCES transactions(id),
    rental_contract_id UUID REFERENCES rental_contracts(id),
    
    -- Métadonnées
    tags TEXT[],
    version INTEGER DEFAULT 1,
    parent_document_id UUID REFERENCES documents(id),
    
    -- Sécurité
    is_confidential BOOLEAN DEFAULT false,
    access_level VARCHAR(20) DEFAULT 'private', -- public, private, restricted
    expiry_date DATE,
    
    -- Signatures
    requires_signature BOOLEAN DEFAULT false,
    signed_at TIMESTAMP WITH TIME ZONE,
    signed_by UUID REFERENCES users(id),
    signature_data JSONB,
    
    uploaded_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 9. MARKETING ET COMMUNICATION
-- ==================================================

-- Campagnes marketing
CREATE TABLE marketing_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50), -- 'email', 'sms', 'social_media', 'advertising'
    
    target_audience JSONB, -- Critères de ciblage
    content JSONB, -- Contenu de la campagne
    
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    
    budget DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    
    status VARCHAR(20) DEFAULT 'draft', -- draft, active, paused, completed
    
    metrics JSONB DEFAULT '{}'::jsonb, -- Métriques de performance
    
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Publications sur portails
CREATE TABLE portal_publications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID REFERENCES properties(id) ON DELETE CASCADE,
    
    portal_name VARCHAR(100) NOT NULL,
    portal_reference VARCHAR(100),
    
    published_at TIMESTAMP WITH TIME ZONE,
    unpublished_at TIMESTAMP WITH TIME ZONE,
    
    status VARCHAR(20) DEFAULT 'active', -- active, paused, rejected, expired
    rejection_reason TEXT,
    
    views_count INTEGER DEFAULT 0,
    contacts_count INTEGER DEFAULT 0,
    
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(20) DEFAULT 'pending',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 10. GESTION FINANCIÈRE
-- ==================================================

-- Factures
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'commission', 'management', 'service'
    
    client_id UUID REFERENCES contacts(id),
    property_id UUID REFERENCES properties(id),
    transaction_id UUID REFERENCES transactions(id),
    
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    
    subtotal DECIMAL(12,2) NOT NULL,
    tax_rate DECIMAL(5,2) DEFAULT 20.00,
    tax_amount DECIMAL(12,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    
    paid_amount DECIMAL(12,2) DEFAULT 0,
    payment_status payment_status DEFAULT 'pending',
    payment_date DATE,
    payment_method VARCHAR(50),
    
    notes TEXT,
    terms TEXT,
    
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Lignes de facture
CREATE TABLE invoice_lines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
    
    description TEXT NOT NULL,
    quantity DECIMAL(10,3) DEFAULT 1,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    
    tax_rate DECIMAL(5,2),
    tax_amount DECIMAL(12,2),
    
    sort_order INTEGER DEFAULT 0
);

-- ==================================================
-- 11. ANALYTICS ET REPORTING
-- ==================================================

-- KPIs et métriques
CREATE TABLE metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_type VARCHAR(50), -- 'count', 'amount', 'percentage', 'duration'
    
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    
    dimensions JSONB DEFAULT '{}'::jsonb, -- Dimensions pour drill-down
    
    calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 12. SYSTÈME DE NOTIFICATIONS
-- ==================================================

CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    
    data JSONB DEFAULT '{}'::jsonb,
    
    priority priority_level DEFAULT 'medium',
    
    read_at TIMESTAMP WITH TIME ZONE,
    dismissed_at TIMESTAMP WITH TIME ZONE,
    
    expires_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- 13. AUDIT ET HISTORIQUE
-- ==================================================

CREATE TABLE audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agency_id UUID REFERENCES agencies(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    
    table_name VARCHAR(100) NOT NULL,
    record_id UUID NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    
    ip_address INET,
    user_agent TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ==================================================
-- INDEX ET CONTRAINTES
-- ==================================================

-- Index de performance
CREATE INDEX idx_properties_agency_type_status ON properties(agency_id, type, status);
CREATE INDEX idx_properties_price_range ON properties(price, type) WHERE status = 'available';
CREATE INDEX idx_properties_coordinates ON properties USING GIST(coordinates);
CREATE INDEX idx_properties_search ON properties USING GIN(to_tsvector('french', title || ' ' || description));

CREATE INDEX idx_contacts_agency_type ON contacts(agency_id, type);
CREATE INDEX idx_contacts_search ON contacts USING GIN(to_tsvector('french', first_name || ' ' || last_name || ' ' || COALESCE(company_name, '')));
CREATE INDEX idx_contacts_tags ON contacts USING GIN(tags);

CREATE INDEX idx_appointments_date_agent ON appointments(scheduled_at, agent_id);
CREATE INDEX idx_appointments_property_date ON appointments(property_id, scheduled_at);

CREATE INDEX idx_rent_payments_contract_due ON rent_payments(rental_contract_id, due_date);
CREATE INDEX idx_rent_payments_status ON rent_payments(status, due_date);

CREATE INDEX idx_audit_log_table_record ON audit_log(table_name, record_id);
CREATE INDEX idx_audit_log_timestamp ON audit_log(created_at);

-- ==================================================
-- VUES MÉTIER
-- ==================================================

-- Vue des propriétés disponibles avec détails
CREATE VIEW v_available_properties AS
SELECT 
    p.*,
    c.first_name || ' ' || c.last_name AS owner_name,
    u.first_name || ' ' || u.last_name AS agent_name,
    COUNT(pm.id) AS media_count,
    AVG(vf.interest_level) AS avg_interest_level
FROM properties p
LEFT JOIN contacts c ON p.owner_id = c.id
LEFT JOIN users u ON p.assigned_agent_id = u.id
LEFT JOIN property_media pm ON p.id = pm.property_id
LEFT JOIN appointments a ON p.id = a.property_id AND a.type = 'visit'
LEFT JOIN visit_feedback vf ON a.id = vf.appointment_id
WHERE p.status = 'available' AND p.is_published = true
GROUP BY p.id, c.first_name, c.last_name, u.first_name, u.last_name;

-- Vue du pipeline de ventes
CREATE VIEW v_sales_pipeline AS
SELECT 
    t.*,
    p.reference AS property_reference,
    p.title AS property_title,
    p.price AS property_price,
    buyer.first_name || ' ' || buyer.last_name AS buyer_name,
    seller.first_name || ' ' || seller.last_name AS seller_name,
    agent.first_name || ' ' || agent.last_name AS agent_name,
    CASE 
        WHEN t.status = 'initiated' THEN 1
        WHEN t.status = 'offer_accepted' THEN 2
        WHEN t.status = 'financing_approved' THEN 3
        WHEN t.status = 'signed' THEN 4
        WHEN t.status = 'completed' THEN 5
        ELSE 0
    END AS pipeline_stage
FROM transactions t
JOIN properties p ON t.property_id = p.id
LEFT JOIN contacts buyer ON t.buyer_id = buyer.id
LEFT JOIN contacts seller ON t.seller_id = seller.id
LEFT JOIN users agent ON t.agent_id = agent.id
WHERE t.type = 'sale';

-- Vue des loyers à encaisser
CREATE VIEW v_rent_collection AS
SELECT 
    rp.*,
    rc.contract_number,
    p.reference AS property_reference,
    p.address->>'street' AS property_address,
    tenant.first_name || ' ' || tenant.last_name AS tenant_name,
    tenant.email AS tenant_email,
    tenant.phone AS tenant_phone,
    CURRENT_DATE - rp.due_date AS days_overdue
FROM rent_payments rp
JOIN rental_contracts rc ON rp.rental_contract_id = rc.id
JOIN properties p ON rc.property_id = p.id
JOIN contacts tenant ON rc.tenant_id = tenant.id
WHERE rp.status IN ('pending', 'overdue')
ORDER BY rp.due_date;

-- Vue des KPIs par agent
CREATE VIEW v_agent_kpis AS
SELECT 
    u.id AS agent_id,
    u.first_name || ' ' || u.last_name AS agent_name,
    COUNT(DISTINCT p.id) AS properties_managed,
    COUNT(DISTINCT CASE WHEN t.status = 'completed' AND t.type = 'sale' THEN t.id END) AS sales_completed,
    COUNT(DISTINCT CASE WHEN rc.status = 'active' THEN rc.id END) AS active_rentals,
    SUM(CASE WHEN t.status = 'completed' AND t.type = 'sale' THEN t.commission_amount ELSE 0 END) AS sales_commission,
    COUNT(DISTINCT a.id) FILTER (WHERE a.scheduled_at >= CURRENT_DATE - INTERVAL '30 days') AS visits_last_30_days,
    AVG(vf.interest_level) AS avg_visit_rating
FROM users u
LEFT JOIN properties p ON u.id = p.assigned_agent_id
LEFT JOIN transactions t ON u.id = t.agent_id
LEFT JOIN rental_contracts rc ON u.id = rc.agent_id
LEFT JOIN appointments a ON u.id = a.agent_id AND a.type = 'visit'
LEFT JOIN visit_feedback vf ON a.id = vf.appointment_id
WHERE u.role = 'agent' AND u.is_active = true
GROUP BY u.id, u.first_name, u.last_name;

-- ==================================================
-- FONCTIONS UTILITAIRES
-- ==================================================

-- Fonction de calcul de distance entre deux points
CREATE OR REPLACE FUNCTION calculate_distance(point1 GEOGRAPHY, point2 GEOGRAPHY)
RETURNS DECIMAL(10,2) AS $$
BEGIN
    RETURN ST_Distance(point1, point2) / 1000; -- Distance en kilomètres
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Fonction de génération de référence unique pour les propriétés
CREATE OR REPLACE FUNCTION generate_property_reference(agency_id UUID, property_type property_type)
RETURNS TEXT AS $$
DECLARE
    prefix TEXT;
    counter INTEGER;
    reference TEXT;
BEGIN
    -- Définir le préfixe selon le type
    CASE property_type
        WHEN 'apartment' THEN prefix := 'APT';
        WHEN 'house' THEN prefix := 'HOU';
        WHEN 'land' THEN prefix := 'LAN';
        WHEN 'commercial' THEN prefix := 'COM';
        ELSE prefix := 'GEN';
    END CASE;
    
    -- Obtenir le prochain numéro
    SELECT COALESCE(MAX(CAST(SUBSTRING(reference FROM '\d+$') AS INTEGER)), 0) + 1
    INTO counter
    FROM properties 
    WHERE agency_id = agency_id AND reference LIKE prefix || '%';
    
    reference := prefix || LPAD(counter::TEXT, 6, '0');
    
    RETURN reference;
END;
$$ LANGUAGE plpgsql;

-- Fonction de calcul du score de contact
CREATE OR REPLACE FUNCTION calculate_contact_score(contact_id UUID)
RETURNS INTEGER AS $
DECLARE
    score INTEGER := 0;
    interaction_count INTEGER;
    last_interaction TIMESTAMP;
    budget_ratio DECIMAL;
    visit_count INTEGER;
BEGIN
    -- Score basé sur les interactions
    SELECT COUNT(*), MAX(created_at)
    INTO interaction_count, last_interaction
    FROM contact_interactions
    WHERE contact_interactions.contact_id = calculate_contact_score.contact_id;
    
    -- Points pour les interactions
    score := score + LEAST(interaction_count * 5, 50);
    
    -- Points pour la récence des interactions
    IF last_interaction > CURRENT_DATE - INTERVAL '7 days' THEN
        score := score + 20;
    ELSIF last_interaction > CURRENT_DATE - INTERVAL '30 days' THEN
        score := score + 10;
    END IF;
    
    -- Score basé sur les visites
    SELECT COUNT(*)
    INTO visit_count
    FROM appointments a
    WHERE a.contact_id = calculate_contact_score.contact_id
    AND a.type = 'visit'
    AND a.status = 'completed';
    
    score := score + LEAST(visit_count * 10, 30);
    
    -- Score basé sur le budget
    SELECT 
        CASE 
            WHEN budget_max > 500000 THEN 30
            WHEN budget_max > 300000 THEN 20
            WHEN budget_max > 150000 THEN 10
            ELSE 5
        END
    INTO budget_ratio
    FROM contacts c
    WHERE c.id = calculate_contact_score.contact_id;
    
    score := score + COALESCE(budget_ratio, 0);
    
    RETURN LEAST(score, 100); -- Score max de 100
END;
$ LANGUAGE plpgsql;

-- Fonction de calcul de la rentabilité locative
CREATE OR REPLACE FUNCTION calculate_rental_yield(property_id UUID)
RETURNS DECIMAL(5,2) AS $
DECLARE
    annual_rent DECIMAL(12,2);
    property_price DECIMAL(12,2);
    yield DECIMAL(5,2);
BEGIN
    SELECT 
        p.price,
        COALESCE(rc.monthly_rent * 12, 0)
    INTO property_price, annual_rent
    FROM properties p
    LEFT JOIN rental_contracts rc ON p.id = rc.property_id AND rc.status = 'active'
    WHERE p.id = calculate_rental_yield.property_id;
    
    IF property_price > 0 AND annual_rent > 0 THEN
        yield := (annual_rent / property_price) * 100;
    ELSE
        yield := 0;
    END IF;
    
    RETURN yield;
END;
$ LANGUAGE plpgsql;

-- Fonction de recherche de propriétés par critères
CREATE OR REPLACE FUNCTION search_properties(
    p_agency_id UUID,
    p_type property_type DEFAULT NULL,
    p_transaction_type transaction_type DEFAULT NULL,
    p_min_price DECIMAL DEFAULT NULL,
    p_max_price DECIMAL DEFAULT NULL,
    p_min_surface DECIMAL DEFAULT NULL,
    p_max_surface DECIMAL DEFAULT NULL,
    p_min_rooms INTEGER DEFAULT NULL,
    p_location_point GEOGRAPHY DEFAULT NULL,
    p_radius_km DECIMAL DEFAULT NULL,
    p_limit INTEGER DEFAULT 50
)
RETURNS TABLE (
    property_id UUID,
    reference VARCHAR,
    title VARCHAR,
    price DECIMAL,
    surface_living DECIMAL,
    rooms_total INTEGER,
    distance_km DECIMAL
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.reference,
        p.title,
        p.price,
        p.surface_living,
        p.rooms_total,
        CASE 
            WHEN p_location_point IS NOT NULL 
            THEN calculate_distance(p.coordinates, p_location_point)
            ELSE NULL 
        END AS distance_km
    FROM properties p
    WHERE p.agency_id = p_agency_id
    AND p.status = 'available'
    AND p.is_published = true
    AND (p_type IS NULL OR p.type = p_type)
    AND (p_transaction_type IS NULL OR p.transaction_type = p_transaction_type)
    AND (p_min_price IS NULL OR p.price >= p_min_price)
    AND (p_max_price IS NULL OR p.price <= p_max_price)
    AND (p_min_surface IS NULL OR p.surface_living >= p_min_surface)
    AND (p_max_surface IS NULL OR p.surface_living <= p_max_surface)
    AND (p_min_rooms IS NULL OR p.rooms_total >= p_min_rooms)
    AND (p_location_point IS NULL OR p_radius_km IS NULL OR 
         ST_DWithin(p.coordinates, p_location_point, p_radius_km * 1000))
    ORDER BY 
        CASE WHEN p_location_point IS NOT NULL 
        THEN ST_Distance(p.coordinates, p_location_point) 
        ELSE 0 END
    LIMIT p_limit;
END;
$ LANGUAGE plpgsql;

-- ==================================================
-- PROCÉDURES STOCKÉES
-- ==================================================

-- Procédure de génération automatique des appels de loyer
CREATE OR REPLACE PROCEDURE generate_monthly_rent_calls()
LANGUAGE plpgsql AS $
DECLARE
    contract_rec RECORD;
    next_due_date DATE;
BEGIN
    FOR contract_rec IN 
        SELECT * FROM rental_contracts 
        WHERE status = 'active'
    LOOP
        -- Calculer la prochaine échéance
        next_due_date := DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day';
        
        -- Vérifier si l'appel n'existe pas déjà
        IF NOT EXISTS (
            SELECT 1 FROM rent_payments 
            WHERE rental_contract_id = contract_rec.id 
            AND period_start = DATE_TRUNC('month', next_due_date)
        ) THEN
            INSERT INTO rent_payments (
                rental_contract_id,
                period_start,
                period_end,
                due_date,
                rent_amount,
                charges_amount,
                total_amount
            ) VALUES (
                contract_rec.id,
                DATE_TRUNC('month', next_due_date),
                next_due_date,
                next_due_date,
                contract_rec.monthly_rent,
                contract_rec.charges,
                contract_rec.monthly_rent + COALESCE(contract_rec.charges, 0)
            );
        END IF;
    END LOOP;
    
    COMMIT;
END;
$;

-- Procédure de nettoyage des données obsolètes
CREATE OR REPLACE PROCEDURE cleanup_old_data()
LANGUAGE plpgsql AS $
BEGIN
    -- Supprimer les notifications expirées
    DELETE FROM notifications 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    -- Supprimer les interactions anciennes (plus de 7 ans)
    DELETE FROM contact_interactions 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '7 years';
    
    -- Supprimer les logs d'audit anciens (plus de 2 ans)
    DELETE FROM audit_log 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '2 years';
    
    -- Marquer les mandats expirés
    UPDATE mandates 
    SET status = 'expired' 
    WHERE end_date < CURRENT_DATE AND status = 'active';
    
    COMMIT;
END;
$;

-- Procédure de calcul des commissions
CREATE OR REPLACE PROCEDURE calculate_agent_commissions(
    p_period_start DATE,
    p_period_end DATE
)
LANGUAGE plpgsql AS $
DECLARE
    agent_rec RECORD;
    commission_total DECIMAL(12,2);
BEGIN
    FOR agent_rec IN 
        SELECT id, first_name, last_name FROM users WHERE role = 'agent' AND is_active = true
    LOOP
        SELECT COALESCE(SUM(commission_amount), 0)
        INTO commission_total
        FROM transactions
        WHERE agent_id = agent_rec.id
        AND completion_date BETWEEN p_period_start AND p_period_end
        AND status = 'completed';
        
        -- Insérer la métrique
        INSERT INTO metrics (
            agency_id,
            user_id,
            metric_name,
            metric_value,
            metric_type,
            period_start,
            period_end
        ) VALUES (
            (SELECT agency_id FROM users WHERE id = agent_rec.id),
            agent_rec.id,
            'commission_total',
            commission_total,
            'amount',
            p_period_start,
            p_period_end
        );
    END LOOP;
    
    COMMIT;
END;
$;

-- ==================================================
-- TRIGGERS
-- ==================================================

-- Trigger pour mise à jour automatique du timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Application du trigger sur toutes les tables avec updated_at
CREATE TRIGGER trigger_agencies_updated_at BEFORE UPDATE ON agencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_contacts_updated_at BEFORE UPDATE ON contacts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_properties_updated_at BEFORE UPDATE ON properties FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_mandates_updated_at BEFORE UPDATE ON mandates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_appointments_updated_at BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_rental_contracts_updated_at BEFORE UPDATE ON rental_contracts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_rent_payments_updated_at BEFORE UPDATE ON rent_payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER trigger_maintenance_requests_updated_at BEFORE UPDATE ON maintenance_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger pour audit log
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $
DECLARE
    audit_user_id UUID;
    audit_agency_id UUID;
BEGIN
    -- Récupérer l'utilisateur courant (à adapter selon votre système d'auth)
    audit_user_id := COALESCE(current_setting('app.current_user_id', true)::UUID, NULL);
    audit_agency_id := COALESCE(current_setting('app.current_agency_id', true)::UUID, NULL);
    
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (
            agency_id, user_id, table_name, record_id, action, old_values
        ) VALUES (
            audit_agency_id, audit_user_id, TG_TABLE_NAME, OLD.id, 'DELETE', row_to_json(OLD)
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (
            agency_id, user_id, table_name, record_id, action, old_values, new_values, changed_fields
        ) VALUES (
            audit_agency_id, audit_user_id, TG_TABLE_NAME, NEW.id, 'UPDATE', 
            row_to_json(OLD), row_to_json(NEW),
            ARRAY(SELECT key FROM jsonb_each(row_to_json(OLD)::jsonb) WHERE row_to_json(OLD)::jsonb -> key != row_to_json(NEW)::jsonb -> key)
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (
            agency_id, user_id, table_name, record_id, action, new_values
        ) VALUES (
            audit_agency_id, audit_user_id, TG_TABLE_NAME, NEW.id, 'INSERT', row_to_json(NEW)
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$ LANGUAGE plpgsql;

-- Application du trigger d'audit sur les tables sensibles
CREATE TRIGGER audit_properties AFTER INSERT OR UPDATE OR DELETE ON properties FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_contacts AFTER INSERT OR UPDATE OR DELETE ON contacts FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_transactions AFTER INSERT OR UPDATE OR DELETE ON transactions FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_rental_contracts AFTER INSERT OR UPDATE OR DELETE ON rental_contracts FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
CREATE TRIGGER audit_users AFTER INSERT OR UPDATE OR DELETE ON users FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

-- Trigger pour génération automatique de référence propriété
CREATE OR REPLACE FUNCTION generate_property_reference_trigger()
RETURNS TRIGGER AS $
BEGIN
    IF NEW.reference IS NULL OR NEW.reference = '' THEN
        NEW.reference := generate_property_reference(NEW.agency_id, NEW.type);
    END IF;
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_property_reference BEFORE INSERT ON properties FOR EACH ROW EXECUTE FUNCTION generate_property_reference_trigger();

-- Trigger pour mise à jour du score de contact
CREATE OR REPLACE FUNCTION update_contact_score_trigger()
RETURNS TRIGGER AS $
BEGIN
    UPDATE contacts 
    SET score = calculate_contact_score(NEW.contact_id)
    WHERE id = NEW.contact_id;
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_contact_score_update AFTER INSERT ON contact_interactions FOR EACH ROW EXECUTE FUNCTION update_contact_score_trigger();

-- Trigger pour mise à jour du statut des loyers impayés
CREATE OR REPLACE FUNCTION update_rent_payment_status()
RETURNS TRIGGER AS $
BEGIN
    -- Marquer comme en retard si la date d'échéance est dépassée
    IF NEW.due_date < CURRENT_DATE AND NEW.status = 'pending' THEN
        NEW.status := 'overdue';
    END IF;
    
    -- Marquer comme payé si le montant payé égale le montant total
    IF NEW.paid_amount >= NEW.total_amount AND NEW.status IN ('pending', 'overdue') THEN
        NEW.status := 'paid';
        NEW.payment_date := CURRENT_DATE;
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_rent_payment_status BEFORE UPDATE ON rent_payments FOR EACH ROW EXECUTE FUNCTION update_rent_payment_status();

-- Trigger pour notifications automatiques
CREATE OR REPLACE FUNCTION create_notification_trigger()
RETURNS TRIGGER AS $
BEGIN
    -- Notification pour nouveau contact
    IF TG_TABLE_NAME = 'contacts' AND TG_OP = 'INSERT' THEN
        INSERT INTO notifications (agency_id, user_id, type, title, message, data)
        VALUES (
            NEW.agency_id,
            NEW.assigned_agent_id,
            'new_contact',
            'Nouveau contact',
            'Un nouveau contact a été créé : ' || COALESCE(NEW.first_name || ' ' || NEW.last_name, NEW.company_name),
            jsonb_build_object('contact_id', NEW.id)
        );
    END IF;
    
    -- Notification pour nouvelle visite
    IF TG_TABLE_NAME = 'appointments' AND TG_OP = 'INSERT' AND NEW.type = 'visit' THEN
        INSERT INTO notifications (agency_id, user_id, type, title, message, data)
        VALUES (
            (SELECT agency_id FROM users WHERE id = NEW.agent_id),
            NEW.agent_id,
            'new_visit',
            'Nouvelle visite programmée',
            'Visite programmée le ' || NEW.scheduled_at::TEXT,
            jsonb_build_object('appointment_id', NEW.id)
        );
    END IF;
    
    -- Notification pour loyer impayé
    IF TG_TABLE_NAME = 'rent_payments' AND TG_OP = 'UPDATE' AND OLD.status = 'pending' AND NEW.status = 'overdue' THEN
        INSERT INTO notifications (agency_id, user_id, type, title, message, data, priority)
        VALUES (
            (SELECT p.agency_id FROM rental_contracts rc JOIN properties p ON rc.property_id = p.id WHERE rc.id = NEW.rental_contract_id),
            (SELECT rc.agent_id FROM rental_contracts rc WHERE rc.id = NEW.rental_contract_id),
            'rent_overdue',
            'Loyer impayé',
            'Un loyer est en retard de paiement',
            jsonb_build_object('rent_payment_id', NEW.id),
            'high'
        );
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_notifications_contacts AFTER INSERT ON contacts FOR EACH ROW EXECUTE FUNCTION create_notification_trigger();
CREATE TRIGGER trigger_notifications_appointments AFTER INSERT ON appointments FOR EACH ROW EXECUTE FUNCTION create_notification_trigger();
CREATE TRIGGER trigger_notifications_rent_payments AFTER UPDATE ON rent_payments FOR EACH ROW EXECUTE FUNCTION create_notification_trigger();

-- ==================================================
-- VUES MÉTIER AVANCÉES
-- ==================================================

-- Vue des statistiques de marché par secteur
CREATE VIEW v_market_statistics AS
SELECT 
    p.address->>'city' AS city,
    p.address->>'postal_code' AS postal_code,
    p.type,
    p.transaction_type,
    COUNT(*) AS properties_count,
    AVG(p.price) AS avg_price,
    MIN(p.price) AS min_price,
    MAX(p.price) AS max_price,
    AVG(p.price_per_sqm) AS avg_price_per_sqm,
    AVG(p.surface_living) AS avg_surface,
    COUNT(CASE WHEN p.status = 'sold' THEN 1 END) AS sold_count,
    COUNT(CASE WHEN p.status = 'available' THEN 1 END) AS available_count
FROM properties p
WHERE p.is_published = true
GROUP BY p.address->>'city', p.address->>'postal_code', p.type, p.transaction_type;

-- Vue du tableau de bord financier
CREATE VIEW v_financial_dashboard AS
SELECT 
    i.agency_id,
    DATE_TRUNC('month', i.issue_date) AS month,
    SUM(CASE WHEN i.payment_status = 'paid' THEN i.total_amount ELSE 0 END) AS revenue,
    SUM(CASE WHEN i.payment_status = 'pending' THEN i.total_amount ELSE 0 END) AS pending_revenue,
    SUM(CASE WHEN i.payment_status = 'overdue' THEN i.total_amount ELSE 0 END) AS overdue_revenue,
    COUNT(CASE WHEN i.payment_status = 'paid' THEN 1 END) AS paid_invoices,
    COUNT(CASE WHEN i.payment_status = 'pending' THEN 1 END) AS pending_invoices
FROM invoices i
GROUP BY i.agency_id, DATE_TRUNC('month', i.issue_date);

-- Vue des propriétés avec alertes
CREATE VIEW v_property_alerts AS
SELECT 
    p.id,
    p.reference,
    p.title,
    p.status,
    CASE 
        WHEN m.end_date < CURRENT_DATE + INTERVAL '30 days' THEN 'mandate_expiring'
        WHEN p.created_at < CURRENT_DATE - INTERVAL '90 days' AND p.status = 'available' THEN 'long_on_market'
        WHEN p.visit_count = 0 AND p.created_at < CURRENT_DATE - INTERVAL '30 days' THEN 'no_visits'
        WHEN AVG(vf.interest_level) < 2 THEN 'low_interest'
        ELSE NULL
    END AS alert_type,
    m.end_date AS mandate_end_date,
    p.visit_count,
    AVG(vf.interest_level) AS avg_interest
FROM properties p
LEFT JOIN mandates m ON p.id = m.property_id AND m.status = 'active'
LEFT JOIN appointments a ON p.id = a.property_id AND a.type = 'visit'
LEFT JOIN visit_feedback vf ON a.id = vf.appointment_id
GROUP BY p.id, p.reference, p.title, p.status, p.created_at, p.visit_count, m.end_date
HAVING CASE 
    WHEN m.end_date < CURRENT_DATE + INTERVAL '30 days' THEN true
    WHEN p.created_at < CURRENT_DATE - INTERVAL '90 days' AND p.status = 'available' THEN true
    WHEN p.visit_count = 0 AND p.created_at < CURRENT_DATE - INTERVAL '30 days' THEN true
    WHEN AVG(vf.interest_level) < 2 THEN true
    ELSE false
END;

-- ==================================================
-- FONCTIONS DE RECHERCHE AVANCÉE
-- ==================================================

-- Fonction de recherche full-text sur les propriétés
CREATE OR REPLACE FUNCTION search_properties_fulltext(
    p_agency_id UUID,
    p_search_term TEXT,
    p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    property_id UUID,
    reference VARCHAR,
    title VARCHAR,
    description TEXT,
    rank REAL
) AS $
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.reference,
        p.title,
        p.description,
        ts_rank(
            to_tsvector('french', p.title || ' ' || COALESCE(p.description, '') || ' ' || p.address::TEXT),
            plainto_tsquery('french', p_search_term)
        ) AS rank
    FROM properties p
    WHERE p.agency_id = p_agency_id
    AND p.is_published = true
    AND to_tsvector('french', p.title || ' ' || COALESCE(p.description, '') || ' ' || p.address::TEXT) @@ plainto_tsquery('french', p_search_term)
    ORDER BY rank DESC
    LIMIT p_limit;
END;
$ LANGUAGE plpgsql;

-- ==================================================
-- JOBS ET TÂCHES PROGRAMMÉES
-- ==================================================

-- Fonction pour programmer les tâches récurrentes (à utiliser avec pg_cron)
CREATE OR REPLACE FUNCTION schedule_recurring_tasks()
RETURNS VOID AS $
BEGIN
    -- Générer les appels de loyer (1er de chaque mois)
    -- SELECT cron.schedule('generate-rent-calls', '0 0 1 * *', 'CALL generate_monthly_rent_calls();');
    
    -- Nettoyage des données (chaque dimanche à 2h)
    -- SELECT cron.schedule('cleanup-data', '0 2 * * 0', 'CALL cleanup_old_data();');
    
    -- Mise à jour des scores de contacts (tous les jours à 3h)
    -- SELECT cron.schedule('update-scores', '0 3 * * *', 'UPDATE contacts SET score = calculate_contact_score(id);');
    
    RAISE NOTICE 'Tâches programmées configurées (décommentez les lignes pour activer)';
END;
$ LANGUAGE plpgsql;

-- ==================================================
-- DONNÉES DE DÉMONSTRATION (OPTIONNEL)
-- ==================================================

-- Insertion d'une agence de démonstration
INSERT INTO agencies (name, legal_name, email, phone, address, settings) VALUES 
('Immobilier Plus', 'Immobilier Plus SARL', '<EMAIL>', '0123456789', 
 '{"street": "123 Rue de la République", "city": "Paris", "postal_code": "75001", "country": "France"}',
 '{"currency": "EUR", "language": "fr", "timezone": "Europe/Paris"}'
);

-- Insertion d'un utilisateur administrateur de démonstration
INSERT INTO users (agency_id, email, password_hash, first_name, last_name, role) VALUES 
((SELECT id FROM agencies WHERE name = 'Immobilier Plus'), 
 '<EMAIL>', 
 '$2a$12$example_hash_here', 
 'Admin', 'Demo', 'admin'
);

-- ==================================================
-- PERMISSIONS ET SÉCURITÉ
-- ==================================================

-- Création des rôles de base de données
CREATE ROLE real_estate_admin;
CREATE ROLE real_estate_agent;
CREATE ROLE real_estate_readonly;

-- Permissions pour l'administrateur
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO real_estate_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO real_estate_admin;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO real_estate_admin;

-- Permissions pour les agents
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO real_estate_agent;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO real_estate_agent;
GRANT EXECUTE ON FUNCTION search_properties, calculate_rental_yield, search_properties_fulltext TO real_estate_agent;

-- Permissions en lecture seule
GRANT SELECT ON ALL TABLES IN SCHEMA public TO real_estate_readonly;
GRANT EXECUTE ON FUNCTION search_properties, search_properties_fulltext TO real_estate_readonly;

-- Politique de sécurité au niveau ligne (RLS) pour isolation par agence
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Politique pour les propriétés (exemple)
CREATE POLICY properties_agency_isolation ON properties
    FOR ALL TO real_estate_agent
    USING (agency_id = current_setting('app.current_agency_id')::UUID);

-- ==================================================
-- COMMENTAIRES DE DOCUMENTATION
-- ==================================================

COMMENT ON TABLE properties IS 'Table principale des biens immobiliers avec toutes leurs caractéristiques';
COMMENT ON COLUMN properties.coordinates IS 'Coordonnées géographiques au format PostGIS pour recherche géospatiale';
COMMENT ON COLUMN properties.amenities IS 'Liste des équipements sous forme de tableau (piscine, garage, etc.)';

COMMENT ON TABLE contacts IS 'Gestion unifiée de tous les contacts : clients, prospects, propriétaires, partenaires';
COMMENT ON COLUMN contacts.score IS 'Score calculé automatiquement basé sur les interactions et le potentiel';

COMMENT ON TABLE rental_contracts IS 'Contrats de location avec gestion complète du cycle de vie';
COMMENT ON TABLE rent_payments IS 'Échéancier et suivi des paiements de loyer';

COMMENT ON FUNCTION calculate_contact_score IS 'Calcule le score d''un contact basé sur les interactions, visites et budget';
COMMENT ON FUNCTION search_properties IS 'Recherche multicritères avec géolocalisation et tri par distance';

-- ==================================================
-- FIN DU SCHÉMA
-- ==================================================

SELECT 'Schéma de base de données créé avec succès!' AS status;
