-- =============================================
-- CONFIGURATION INITIALE
-- =============================================
-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- =============================================
-- TABLES DE BASE
-- =============================================

-- ======================
-- UTILISATEURS ET RÔLES
-- ======================
CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE roles IS 'Rôles des utilisateurs dans le système (agent, administrateur, comptable, etc.)';
COMMENT ON COLUMN roles.name IS 'Nom du rôle (ex: agent, admin, comptable)';
COMMENT ON COLUMN roles.description IS 'Description détaillée des permissions du rôle';

CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255)
);

COMMENT ON TABLE users IS 'Table principale des utilisateurs (agents, administrateurs, etc.)';
COMMENT ON COLUMN users.password_hash IS 'Hash du mot de passe (ne jamais stocker en clair)';
COMMENT ON COLUMN users.two_factor_enabled IS 'Indique si l''authentification à 2 facteurs est activée';

CREATE TABLE user_roles (
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE
);

-- ======================
-- CLIENTS ET CRM
-- ======================
CREATE TABLE client_types (
    client_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

INSERT INTO client_types (name, description) VALUES
('acquereur', 'Client cherchant à acheter un bien'),
('vendeur', 'Client souhaitant vendre un bien'),
('locataire', 'Client cherchant à louer un bien'),
('proprietaire', 'Propriétaire de biens gérés par l''agence'),
('investisseur', 'Client investissant dans l''immobilier');

CREATE TABLE clients (
    client_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_type_id UUID NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'France',
    notes TEXT,
    lead_score INTEGER DEFAULT 0,
    is_vip BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_type_id) REFERENCES client_types(client_type_id)
);

COMMENT ON TABLE clients IS 'Base de données centrale des clients (acquéreurs, vendeurs, locataires, propriétaires)';
COMMENT ON COLUMN clients.lead_score IS 'Score du lead (0-100) basé sur l''engagement et les critères de qualification';

CREATE TABLE client_preferences (
    preference_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL,
    min_price NUMERIC(12,2),
    max_price NUMERIC(12,2),
    min_surface NUMERIC(10,2),
    max_surface NUMERIC(10,2),
    min_rooms INTEGER,
    property_type VARCHAR(50)[],
    locations VARCHAR(100)[],
    other_criteria JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE
);

COMMENT ON TABLE client_preferences IS 'Préférences de recherche des clients pour le matching automatique';
COMMENT ON COLUMN client_preferences.property_type IS 'Types de biens souhaités (ex: {"appartement", "maison"})';
COMMENT ON COLUMN client_preferences.locations IS 'Localisations souhaitées (ex: {"Paris 15", "Boulogne"})';

CREATE TABLE interactions (
    interaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL,
    user_id UUID NOT NULL,
    interaction_type VARCHAR(50) NOT NULL,
    notes TEXT,
    contact_channel VARCHAR(50),
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

COMMENT ON TABLE interactions IS 'Historique des interactions avec les clients (appels, emails, rendez-vous)';
COMMENT ON COLUMN interactions.interaction_type IS 'Type d''interaction (appel, email, visite, etc.)';
COMMENT ON COLUMN interactions.contact_channel IS 'Canal de contact (téléphone, email, WhatsApp, etc.)';

-- ======================
-- BIENS IMMOBILIERS
-- ======================
CREATE TABLE property_types (
    property_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_residential BOOLEAN DEFAULT TRUE
);

INSERT INTO property_types (name, description, is_residential) VALUES
('appartement', 'Logement dans un immeuble', TRUE),
('maison', 'Logement individuel', TRUE),
('terrain', 'Terrain à bâtir', TRUE),
('local_commercial', 'Local à usage commercial', FALSE),
('bureaux', 'Espaces de bureaux', FALSE),
('parking', 'Place de parking', TRUE),
('garage', 'Garage', TRUE);

CREATE TABLE property_statuses (
    status_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_available BOOLEAN DEFAULT TRUE
);

INSERT INTO property_statuses (name, description, is_available) VALUES
('disponible', 'Bien disponible à la vente/location', TRUE),
('sous_offre', 'Bien avec offre en cours', TRUE),
('vendu', 'Bien vendu', FALSE),
('loue', 'Bien loué', FALSE),
('reserve', 'Bien réservé', FALSE),
('mandat_expire', 'Mandat expiré', FALSE),
('en_travaux', 'Bien en travaux', TRUE);

CREATE TABLE properties (
    property_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(50) UNIQUE NOT NULL,
    property_type_id UUID NOT NULL,
    status_id UUID NOT NULL DEFAULT (SELECT status_id FROM property_statuses WHERE name = 'disponible'),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) DEFAULT 'France',
    latitude NUMERIC(10, 7),
    longitude NUMERIC(10, 7),
    surface NUMERIC(10, 2) NOT NULL,
    land_surface NUMERIC(10, 2),
    rooms INTEGER NOT NULL,
    bedrooms INTEGER,
    bathrooms INTEGER,
    floor INTEGER,
    total_floors INTEGER,
    year_built INTEGER,
    energy_rating VARCHAR(10),
    energy_consumption NUMERIC(8, 2),
    energy_emissions NUMERIC(8, 2),
    heating_type VARCHAR(50),
    has_elevator BOOLEAN DEFAULT FALSE,
    has_parking BOOLEAN DEFAULT FALSE,
    has_garden BOOLEAN DEFAULT FALSE,
    has_terrace BOOLEAN DEFAULT FALSE,
    has_pool BOOLEAN DEFAULT FALSE,
    is_furnished BOOLEAN DEFAULT FALSE,
    is_new BOOLEAN DEFAULT FALSE,
    is_handicap_accessible BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_type_id) REFERENCES property_types(property_type_id),
    FOREIGN KEY (status_id) REFERENCES property_statuses(status_id)
);

COMMENT ON TABLE properties IS 'Base de données des biens immobiliers avec toutes leurs caractéristiques';
COMMENT ON COLUMN properties.reference IS 'Référence unique du bien dans le système';
COMMENT ON COLUMN properties.energy_rating IS 'Note énergétique (A-G pour DPE)';

CREATE TABLE property_features (
    feature_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    feature_name VARCHAR(100) NOT NULL,
    feature_value VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(property_id) ON DELETE CASCADE
);

CREATE TABLE property_media (
    media_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('photo', 'video', '3d', 'plan')),
    url VARCHAR(500) NOT NULL,
    description TEXT,
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(property_id) ON DELETE CASCADE
);

COMMENT ON TABLE property_media IS 'Gestion des médias associés aux biens (photos, vidéos, visites 3D)';
COMMENT ON COLUMN property_media.media_type IS 'Type de média (photo, video, 3d, plan)';

CREATE TABLE property_documents (
    document_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    valid_from DATE,
    valid_to DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(property_id) ON DELETE CASCADE
);

COMMENT ON TABLE property_documents IS 'Documents associés aux biens (diagnostics, plans, actes)';
COMMENT ON COLUMN property_documents.document_type IS 'Type de document (DPE, amiante, plomb, etc.)';
COMMENT ON COLUMN property_documents.valid_to IS 'Date d''expiration du document (ex: pour les diagnostics)';

-- ======================
-- MANDATS ET TRANSACTIONS
-- ======================
CREATE TABLE mandate_types (
    mandate_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    commission_rate NUMERIC(5, 2) NOT NULL,
    is_exclusive BOOLEAN DEFAULT FALSE
);

INSERT INTO mandate_types (name, description, commission_rate, is_exclusive) VALUES
('exclusif', 'Mandat de vente exclusif', 5.00, TRUE),
('semi_exclusif', 'Mandat de vente semi-exclusif', 4.50, FALSE),
('simple', 'Mandat de vente simple', 4.00, FALSE),
('location', 'Mandat de gestion locative', 8.00, FALSE);

CREATE TABLE mandates (
    mandate_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    client_id UUID NOT NULL,
    mandate_type_id UUID NOT NULL,
    reference VARCHAR(50) UNIQUE NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    price NUMERIC(12, 2) NOT NULL,
    min_price NUMERIC(12, 2),
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(property_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (mandate_type_id) REFERENCES mandate_types(mandate_type_id)
);

COMMENT ON TABLE mandates IS 'Gestion des mandats de vente et de location';
COMMENT ON COLUMN mandates.min_price IS 'Prix minimum acceptable pour les mandats de vente';

CREATE TABLE transaction_stages (
    stage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    stage_order INTEGER NOT NULL,
    is_final BOOLEAN DEFAULT FALSE
);

INSERT INTO transaction_stages (name, description, stage_order, is_final) VALUES
('prospection', 'Recherche de biens correspondants', 1, FALSE),
('visite_programmee', 'Visite planifiée', 2, FALSE),
('visite_effectuee', 'Visite réalisée', 3, FALSE),
('offre_deposee', 'Offre déposée', 4, FALSE),
('offre_acceptee', 'Offre acceptée', 5, FALSE),
('compromis_signe', 'Compromis de vente signé', 6, FALSE),
('financement_en_cours', 'Obtention du financement', 7, FALSE),
('diagnostics_en_cours', 'Réalisation des diagnostics', 8, FALSE),
('acte_definitif', 'Acte authentique signé', 9, TRUE),
('fonds_debloques', 'Fonds débloqués', 10, TRUE);

CREATE TABLE transactions (
    transaction_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    client_id UUID NOT NULL,
    mandate_id UUID,
    stage_id UUID NOT NULL DEFAULT (SELECT stage_id FROM transaction_stages WHERE stage_order = 1),
    price NUMERIC(12, 2) NOT NULL,
    expected_completion_date DATE,
    actual_completion_date DATE,
    is_rental BOOLEAN DEFAULT FALSE,
    lease_duration INTEGER, -- en mois
    monthly_rent NUMERIC(10, 2),
    security_deposit NUMERIC(10, 2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(property_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (mandate_id) REFERENCES mandates(mandate_id),
    FOREIGN KEY (stage_id) REFERENCES transaction_stages(stage_id)
);

COMMENT ON TABLE transactions IS 'Suivi des transactions (ventes et locations) avec étapes personnalisables';
COMMENT ON COLUMN transactions.is_rental IS 'Indique si la transaction est une location';

CREATE TABLE offers (
    offer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL,
    client_id UUID NOT NULL,
    amount NUMERIC(12, 2) NOT NULL,
    is_accepted BOOLEAN,
    is_counteroffer BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id)
);

-- ======================
-- GESTION LOCATIVE
-- ======================
CREATE TABLE leases (
    lease_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    renewal_date DATE,
    rent_index VARCHAR(50) DEFAULT 'IRL',
    rent_index_value NUMERIC(8, 4),
    rent_revision_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id)
);

CREATE TABLE rent_payments (
    payment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lease_id UUID NOT NULL,
    amount NUMERIC(10, 2) NOT NULL,
    payment_date DATE NOT NULL,
    due_date DATE NOT NULL,
    payment_method VARCHAR(50),
    is_paid BOOLEAN DEFAULT FALSE,
    is_overdue BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (lease_id) REFERENCES leases(lease_id)
);

CREATE TABLE maintenance_request_statuses (
    status_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

INSERT INTO maintenance_request_statuses (name, description) VALUES
('signalee', 'Demande signalée par le locataire'),
('en_cours', 'Intervention en cours'),
('planifiee', 'Intervention planifiée'),
('terminee', 'Intervention terminée'),
('annulee', 'Demande annulée');

CREATE TABLE artisans (
    artisan_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_name VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255),
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    postal_code VARCHAR(20),
    specialty VARCHAR(100) NOT NULL,
    is_certified BOOLEAN DEFAULT FALSE,
    rating NUMERIC(3, 2) DEFAULT 0.0,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE maintenance_requests (
    request_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id UUID NOT NULL,
    lease_id UUID,
    client_id UUID, -- locataire
    status_id UUID NOT NULL DEFAULT (SELECT status_id FROM maintenance_request_statuses WHERE name = 'signalee'),
    description TEXT NOT NULL,
    urgency_level INTEGER NOT NULL CHECK (urgency_level BETWEEN 1 AND 5),
    reported_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    artisan_id UUID,
    cost_estimate NUMERIC(10, 2),
    actual_cost NUMERIC(10, 2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (property_id) REFERENCES properties(property_id),
    FOREIGN KEY (lease_id) REFERENCES leases(lease_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (status_id) REFERENCES maintenance_request_statuses(status_id),
    FOREIGN KEY (artisan_id) REFERENCES artisans(artisan_id)
);

CREATE TABLE maintenance_media (
    media_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_id UUID NOT NULL,
    media_type VARCHAR(20) NOT NULL CHECK (media_type IN ('photo', 'video')),
    url VARCHAR(500) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES maintenance_requests(request_id) ON DELETE CASCADE
);

-- ======================
-- DOCUMENTS JURIDIQUES
-- ======================
CREATE TABLE contract_types (
    contract_type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_rental BOOLEAN DEFAULT FALSE,
    is_sale BOOLEAN DEFAULT FALSE
);

INSERT INTO contract_types (name, description, is_rental, is_sale) VALUES
('bail_residence_principale', 'Bail pour résidence principale', TRUE, FALSE),
('bail_meuble', 'Bail pour logement meublé', TRUE, FALSE),
('compromis_vente', 'Compromis de vente', FALSE, TRUE),
('mandat_vente', 'Mandat de vente', FALSE, TRUE),
('mandat_gestion', 'Mandat de gestion locative', TRUE, FALSE);

CREATE TABLE contract_templates (
    template_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contract_type_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    version VARCHAR(20) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contract_type_id) REFERENCES contract_types(contract_type_id)
);

CREATE TABLE contracts (
    contract_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contract_type_id UUID NOT NULL,
    transaction_id UUID,
    property_id UUID,
    client_id UUID NOT NULL,
    reference VARCHAR(50) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'draft',
    signed_at TIMESTAMP WITH TIME ZONE,
    valid_from DATE,
    valid_to DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contract_type_id) REFERENCES contract_types(contract_type_id),
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id),
    FOREIGN KEY (property_id) REFERENCES properties(property_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id)
);

CREATE TABLE signatures (
    signature_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contract_id UUID NOT NULL,
    client_id UUID NOT NULL,
    user_id UUID, -- agent qui a géré la signature
    signature_type VARCHAR(50) NOT NULL, -- DocuSign, Yousign, etc.
    signature_date TIMESTAMP WITH TIME ZONE NOT NULL,
    signature_location VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (contract_id) REFERENCES contracts(contract_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- ======================
-- COMPTABILITÉ ET FINANCES
-- ======================
CREATE TABLE payment_methods (
    method_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

INSERT INTO payment_methods (name, description) VALUES
('virement', 'Virement bancaire'),
('carte_bancaire', 'Carte bancaire'),
('cheque', 'Chèque'),
('especes', 'Espèces'),
('prelevement', 'Prélèvement automatique');

CREATE TABLE invoice_types (
    type_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

INSERT INTO invoice_types (name, description) VALUES
('honoraires', 'Honoraires d''agence'),
('acompte', 'Acompte sur honoraires'),
('loyer', 'Paiement de loyer'),
('charge', 'Paiement de charges'),
('garantie', 'Dépôt de garantie'),
('travaux', 'Facture pour travaux');

CREATE TABLE invoices (
    invoice_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(50) UNIQUE NOT NULL,
    client_id UUID,
    property_id UUID,
    transaction_id UUID,
    invoice_type_id UUID NOT NULL,
    amount NUMERIC(12, 2) NOT NULL,
    vat_rate NUMERIC(5, 2) DEFAULT 20.0,
    vat_amount NUMERIC(12, 2),
    total_amount NUMERIC(12, 2) NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    payment_date DATE,
    payment_method_id UUID,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (property_id) REFERENCES properties(property_id),
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id),
    FOREIGN KEY (invoice_type_id) REFERENCES invoice_types(type_id),
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(method_id)
);

CREATE TABLE commissions (
    commission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL,
    user_id UUID NOT NULL,
    amount NUMERIC(12, 2) NOT NULL,
    rate NUMERIC(5, 2) NOT NULL,
    is_paid BOOLEAN DEFAULT FALSE,
    paid_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (transaction_id) REFERENCES transactions(transaction_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- ======================
-- MARKETING ET COMMUNICATION
-- ======================
CREATE TABLE marketing_channels (
    channel_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

INSERT INTO marketing_channels (name, description) VALUES
('seloger', 'Portail SeLoger'),
('leboncoin', 'Portail Leboncoin'),
('pap', 'Portail PAP'),
('logic_immo', 'Portail Logic-Immo'),
('reseau_social', 'Réseaux sociaux'),
('emailing', 'Campagnes emailing'),
('site_agence', 'Site de l''agence'),
('autre', 'Autre canal');

CREATE TABLE campaigns (
    campaign_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    channel_id UUID NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    budget NUMERIC(12, 2),
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (channel_id) REFERENCES marketing_channels(channel_id)
);

CREATE TABLE campaign_leads (
    campaign_lead_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL,
    client_id UUID NOT NULL,
    lead_source VARCHAR(100),
    conversion_status VARCHAR(20) DEFAULT 'new' CHECK (conversion_status IN ('new', 'contacted', 'qualified', 'converted', 'lost')),
    conversion_date DATE,
    cost_per_lead NUMERIC(10, 2),
    roi NUMERIC(10, 2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (campaign_id) REFERENCES campaigns(campaign_id),
    FOREIGN KEY (client_id) REFERENCES clients(client_id)
);

-- ======================
-- SÉCURITÉ ET AUDIT
-- ======================
CREATE TABLE permissions (
    permission_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT
);

INSERT INTO permissions (name, description) VALUES
('property_view', 'Voir les détails d''un bien'),
('property_create', 'Créer un nouveau bien'),
('property_edit', 'Modifier un bien'),
('property_delete', 'Supprimer un bien'),
('client_view', 'Voir les détails d''un client'),
('client_create', 'Créer un nouveau client'),
('client_edit', 'Modifier un client'),
('client_delete', 'Supprimer un client'),
('transaction_view', 'Voir les détails d''une transaction'),
('transaction_edit', 'Modifier une transaction'),
('invoice_create', 'Créer une facture'),
('invoice_view', 'Voir les factures'),
('report_view', 'Voir les rapports'),
('admin_settings', 'Accéder aux paramètres administratifs');

CREATE TABLE role_permissions (
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE
);

CREATE TABLE audit_logs (
    log_id BIGSERIAL PRIMARY KEY,
    user_id UUID,
    action_type VARCHAR(50) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

COMMENT ON TABLE audit_logs IS 'Journal d''audit pour la traçabilité des modifications';
COMMENT ON COLUMN audit_logs.old_values IS 'Valeurs avant modification (format JSON)';
COMMENT ON COLUMN audit_logs.new_values IS 'Valeurs après modification (format JSON)';

CREATE TABLE data_consent (
    consent_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL,
    consent_type VARCHAR(50) NOT NULL,
    granted BOOLEAN NOT NULL,
    granted_at TIMESTAMP WITH TIME ZONE,
    revoked_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id)
);

-- ======================
-- FONCTIONNALITÉS AVANCÉES
-- ======================
CREATE TABLE alerts (
    alert_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alert_type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    target_id UUID NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'critical')),
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id)
);

COMMENT ON TABLE alerts IS 'Table des alertes système (non-conformité, échéances, etc.)';
COMMENT ON COLUMN alerts.target_type IS 'Type de cible (property, mandate, transaction, etc.)';

CREATE TABLE ai_recommendations (
    recommendation_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID NOT NULL,
    property_id UUID NOT NULL,
    score NUMERIC(5, 2) NOT NULL CHECK (score BETWEEN 0 AND 100),
    reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(client_id),
    FOREIGN KEY (property_id) REFERENCES properties(property_id)
);

-- =============================================
-- VUES
-- =============================================

-- Vue pour le tableau de bord des agents
CREATE VIEW agent_dashboard AS
SELECT 
    u.user_id,
    u.first_name,
    u.last_name,
    COUNT(DISTINCT c.client_id) AS total_clients,
    COUNT(DISTINCT p.property_id) FILTER (WHERE ps.is_available) AS available_properties,
    COUNT(DISTINCT p.property_id) FILTER (WHERE ps.name = 'sous_offre') AS properties_under_offer,
    COUNT(DISTINCT t.transaction_id) FILTER (WHERE t.stage_id = (SELECT stage_id FROM transaction_stages WHERE name = 'offre_acceptee')) AS accepted_offers,
    COUNT(DISTINCT t.transaction_id) FILTER (WHERE t.stage_id = (SELECT stage_id FROM transaction_stages WHERE is_final = TRUE)) AS completed_transactions,
    COALESCE(SUM(i.total_amount) FILTER (WHERE i.status = 'paid'), 0) AS revenue_current_month
FROM users u
LEFT JOIN interactions i2 ON u.user_id = i2.user_id
LEFT JOIN clients c ON i2.client_id = c.client_id
LEFT JOIN mandates m ON c.client_id = m.client_id
LEFT JOIN properties p ON m.property_id = p.property_id
LEFT JOIN property_statuses ps ON p.status_id = ps.status_id
LEFT JOIN transactions t ON p.property_id = t.property_id
LEFT JOIN invoices i ON t.transaction_id = i.transaction_id 
    AND EXTRACT(MONTH FROM i.issue_date) = EXTRACT(MONTH FROM CURRENT_DATE)
    AND EXTRACT(YEAR FROM i.issue_date) = EXTRACT(YEAR FROM CURRENT_DATE)
WHERE 'agent' IN (SELECT r.name FROM user_roles ur JOIN roles r ON ur.role_id = r.role_id WHERE ur.user_id = u.user_id)
GROUP BY u.user_id;

-- Vue pour le tableau de bord locatif
CREATE VIEW rental_dashboard AS
SELECT 
    p.property_id,
    p.reference,
    p.address,
    p.city,
    p.postal_code,
    p.surface,
    l.start_date,
    l.end_date,
    l.rent_revision_date,
    rp.amount AS monthly_rent,
    rp.payment_date AS last_payment_date,
    (SELECT COUNT(*) FROM rent_payments rp2 WHERE rp2.lease_id = l.lease_id AND rp2.is_paid = FALSE) AS overdue_payments,
    (SELECT COUNT(*) FROM maintenance_requests mr WHERE mr.property_id = p.property_id AND mr.status_id = (SELECT status_id FROM maintenance_request_statuses WHERE name = 'en_cours')) AS active_maintenance_requests
FROM properties p
JOIN transactions t ON p.property_id = t.property_id AND t.is_rental = TRUE
JOIN leases l ON t.transaction_id = l.transaction_id
JOIN rent_payments rp ON l.lease_id = rp.lease_id AND rp.payment_date = (SELECT MAX(payment_date) FROM rent_payments WHERE lease_id = l.lease_id);

-- Vue pour les alertes de non-conformité
CREATE VIEW compliance_alerts AS
SELECT 
    'diagnostic' AS alert_type,
    'Expiration du diagnostic' AS title,
    'Le diagnostic ' || pd.document_type || ' pour le bien ' || p.reference || ' expire le ' || pd.valid_to::TEXT AS description,
    p.property_id AS target_id,
    'property' AS target_type,
    'critical' AS severity,
    pd.valid_to AS due_date
FROM property_documents pd
JOIN properties p ON pd.property_id = p.property_id
WHERE pd.valid_to IS NOT NULL 
    AND pd.valid_to < CURRENT_DATE + INTERVAL '30 days'
    AND pd.valid_to >= CURRENT_DATE
    
UNION ALL

SELECT 
    'mandate' AS alert_type,
    'Expiration du mandat' AS title,
    'Le mandat ' || m.reference || ' pour le bien ' || p.reference || ' expire le ' || m.end_date::TEXT AS description,
    m.mandate_id AS target_id,
    'mandate' AS target_type,
    CASE 
        WHEN m.end_date < CURRENT_DATE THEN 'critical'
        ELSE 'warning'
    END AS severity,
    m.end_date AS due_date
FROM mandates m
JOIN properties p ON m.property_id = p.property_id
WHERE m.is_active = TRUE
    AND m.end_date < CURRENT_DATE + INTERVAL '30 days'

UNION ALL

SELECT 
    'lease' AS alert_type,
    'Révision de loyer' AS title,
    'Le loyer pour le bien ' || p.reference || ' doit être révisé le ' || l.rent_revision_date::TEXT AS description,
    l.lease_id AS target_id,
    'lease' AS target_type,
    'info' AS severity,
    l.rent_revision_date AS due_date
FROM leases l
JOIN transactions t ON l.transaction_id = t.transaction_id
JOIN properties p ON t.property_id = p.property_id
WHERE l.rent_revision_date IS NOT NULL
    AND l.rent_revision_date < CURRENT_DATE + INTERVAL '30 days'
    AND l.is_active = TRUE;

-- =============================================
-- FONCTIONS
-- =============================================

-- Fonction pour calculer automatiquement les commissions
CREATE OR REPLACE FUNCTION calculate_commission(
    transaction_id UUID,
    commission_rate NUMERIC(5,2) DEFAULT NULL
) RETURNS NUMERIC(12,2) AS $$
DECLARE
    transaction_price NUMERIC(12,2);
    mandate_rate NUMERIC(5,2);
    calculated_commission NUMERIC(12,2);
BEGIN
    -- Récupérer le prix de la transaction
    SELECT price INTO transaction_price
    FROM transactions
    WHERE transaction_id = calculate_commission.transaction_id;
    
    IF transaction_price IS NULL THEN
        RAISE EXCEPTION 'Transaction non trouvée ou sans prix';
    END IF;
    
    -- Récupérer le taux de commission du mandat si non fourni
    IF commission_rate IS NULL THEN
        SELECT mt.commission_rate INTO mandate_rate
        FROM mandates m
        JOIN mandate_types mt ON m.mandate_type_id = mt.mandate_type_id
        WHERE m.transaction_id = calculate_commission.transaction_id;
        
        IF mandate_rate IS NULL THEN
            RAISE EXCEPTION 'Aucun mandat trouvé pour cette transaction';
        END IF;
        commission_rate := mandate_rate;
    END IF;
    
    -- Calculer la commission
    calculated_commission := transaction_price * (commission_rate / 100.0);
    
    RETURN calculated_commission;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour générer un rapport financier mensuel
CREATE OR REPLACE FUNCTION generate_monthly_financial_report(
    report_month DATE
) RETURNS TABLE (
    month TEXT,
    total_revenue NUMERIC(12,2),
    total_commissions NUMERIC(12,2),
    total_expenses NUMERIC(12,2),
    net_profit NUMERIC(12,2),
    transaction_count INTEGER,
    new_clients INTEGER,
    properties_sold INTEGER,
    properties_rented INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        TO_CHAR(report_month, 'YYYY-MM') AS month,
        COALESCE(SUM(i.total_amount) FILTER (WHERE i.status = 'paid'), 0) AS total_revenue,
        COALESCE(SUM(c.amount), 0) AS total_commissions,
        0::NUMERIC(12,2) AS total_expenses, -- À implémenter avec une table des dépenses
        COALESCE(SUM(i.total_amount) FILTER (WHERE i.status = 'paid'), 0) - COALESCE(SUM(c.amount), 0) AS net_profit,
        COUNT(DISTINCT t.transaction_id) AS transaction_count,
        COUNT(DISTINCT c.client_id) FILTER (WHERE c.created_at >= report_month AND c.created_at < report_month + INTERVAL '1 month') AS new_clients,
        COUNT(DISTINCT t.transaction_id) FILTER (WHERE t.is_rental = FALSE AND t.actual_completion_date IS NOT NULL) AS properties_sold,
        COUNT(DISTINCT t.transaction_id) FILTER (WHERE t.is_rental = TRUE AND t.actual_completion_date IS NOT NULL) AS properties_rented
    FROM transactions t
    LEFT JOIN invoices i ON t.transaction_id = i.transaction_id
        AND EXTRACT(MONTH FROM i.issue_date) = EXTRACT(MONTH FROM report_month)
        AND EXTRACT(YEAR FROM i.issue_date) = EXTRACT(YEAR FROM report_month)
    LEFT JOIN commissions c ON t.transaction_id = c.transaction_id
        AND EXTRACT(MONTH FROM c.created_at) = EXTRACT(MONTH FROM report_month)
        AND EXTRACT(YEAR FROM c.created_at) = EXTRACT(YEAR FROM report_month)
    LEFT JOIN clients c2 ON t.client_id = c2.client_id
    WHERE 
        (t.actual_completion_date IS NULL OR 
        (EXTRACT(MONTH FROM t.actual_completion_date) = EXTRACT(MONTH FROM report_month) AND
         EXTRACT(YEAR FROM t.actual_completion_date) = EXTRACT(YEAR FROM report_month)))
    GROUP BY 1;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour le scoring des leads
CREATE OR REPLACE FUNCTION calculate_lead_score(
    client_id UUID
) RETURNS INTEGER AS $$
DECLARE
    score INTEGER := 0;
    last_interaction_date TIMESTAMP;
    visits_count INTEGER;
    offers_count INTEGER;
BEGIN
    -- Base score
    score := 10;
    
    -- Récupérer la date de la dernière interaction
    SELECT MAX(completed_at) INTO last_interaction_date
    FROM interactions
    WHERE client_id = calculate_lead_score.client_id;
    
    -- Bonus pour les interactions récentes
    IF last_interaction_date IS NOT NULL THEN
        IF last_interaction_date > CURRENT_TIMESTAMP - INTERVAL '7 days' THEN
            score := score + 30;
        ELSIF last_interaction_date > CURRENT_TIMESTAMP - INTERVAL '30 days' THEN
            score := score + 20;
        ELSIF last_interaction_date > CURRENT_TIMESTAMP - INTERVAL '90 days' THEN
            score := score + 10;
        END IF;
    END IF;
    
    -- Bonus pour les visites
    SELECT COUNT(*) INTO visits_count
    FROM interactions
    WHERE client_id = calculate_lead_score.client_id
        AND interaction_type = 'visite_effectuee';
    
    IF visits_count > 0 THEN
        score := score + LEAST(visits_count * 15, 30);
    END IF;
    
    -- Bonus pour les offres
    SELECT COUNT(*) INTO offers_count
    FROM offers o
    JOIN transactions t ON o.transaction_id = t.transaction_id
    WHERE t.client_id = calculate_lead_score.client_id;
    
    IF offers_count > 0 THEN
        score := score + LEAST(offers_count * 25, 30);
    END IF;
    
    -- Appliquer les limites
    score := GREATEST(LEAST(score, 100), 0);
    
    -- Mettre à jour le score dans la table clients
    UPDATE clients
    SET lead_score = score
    WHERE client_id = calculate_lead_score.client_id;
    
    RETURN score;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger pour mettre à jour le timestamp updated_at
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Appliquer le trigger à toutes les tables nécessitant un updated_at
DO $$ 
DECLARE
    table_name TEXT;
BEGIN
    FOR table_name IN 
        SELECT table_name 
        FROM information_schema.columns 
        WHERE column_name = 'updated_at'
    LOOP
        EXECUTE format(
            'CREATE TRIGGER set_updated_at
            BEFORE UPDATE ON %I
            FOR EACH ROW
            EXECUTE FUNCTION update_modified_column()',
            table_name
        );
    END LOOP;
END $$;

-- Trigger pour l'audit des modifications
CREATE OR REPLACE FUNCTION log_audit()
RETURNS TRIGGER AS $$
DECLARE
    old_data JSONB;
    new_data JSONB;
BEGIN
    -- Déterminer le type d'action
    IF TG_OP = 'DELETE' THEN
        old_data = to_jsonb(OLD);
        new_data = NULL;
    ELSIF TG_OP = 'UPDATE' THEN
        old_data = to_jsonb(OLD);
        new_data = to_jsonb(NEW);
    ELSIF TG_OP = 'INSERT' THEN
        old_data = NULL;
        new_data = to_jsonb(NEW);
    END IF;
    
    -- Insérer dans le journal d'audit
    INSERT INTO audit_logs (
        user_id,
        action_type,
        table_name,
        record_id,
        old_values,
        new_values,
        ip_address,
        user_agent
    ) VALUES (
        current_setting('app.current_user_id')::UUID,
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.property_id, OLD.property_id),
        old_data,
        new_data,
        current_setting('app.current_ip_address')::INET,
        current_setting('app.current_user_agent')
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Appliquer le trigger d'audit aux tables sensibles
CREATE TRIGGER audit_properties
AFTER INSERT OR UPDATE OR DELETE ON properties
FOR EACH ROW EXECUTE FUNCTION log_audit();

CREATE TRIGGER audit_clients
AFTER INSERT OR UPDATE OR DELETE ON clients
FOR EACH ROW EXECUTE FUNCTION log_audit();

CREATE TRIGGER audit_transactions
AFTER INSERT OR UPDATE OR DELETE ON transactions
FOR EACH ROW EXECUTE FUNCTION log_audit();

CREATE TRIGGER audit_contracts
AFTER INSERT OR UPDATE OR DELETE ON contracts
FOR EACH ROW EXECUTE FUNCTION log_audit();

-- Trigger pour générer des alertes de non-conformité
CREATE OR REPLACE FUNCTION check_compliance()
RETURNS TRIGGER AS $$
BEGIN
    -- Vérifier les diagnostics expirés
    IF TG_TABLE_NAME = 'property_documents' AND (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        IF NEW.valid_to IS NOT NULL AND NEW.valid_to < CURRENT_DATE + INTERVAL '30 days' THEN
            INSERT INTO alerts (alert_type, title, description, target_id, target_type, severity)
            VALUES (
                'diagnostic',
                'Expiration du diagnostic',
                'Le diagnostic ' || NEW.document_type || ' pour le bien ' || (SELECT reference FROM properties WHERE property_id = NEW.property_id) || ' expire le ' || NEW.valid_to::TEXT,
                NEW.property_id,
                'property',
                CASE WHEN NEW.valid_to < CURRENT_DATE THEN 'critical' ELSE 'warning' END
            );
        END IF;
    END IF;
    
    -- Vérifier les mandats expirés
    IF TG_TABLE_NAME = 'mandates' AND (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        IF NEW.end_date < CURRENT_DATE + INTERVAL '30 days' THEN
            INSERT INTO alerts (alert_type, title, description, target_id, target_type, severity)
            VALUES (
                'mandate',
                'Expiration du mandat',
                'Le mandat ' || NEW.reference || ' pour le bien ' || (SELECT reference FROM properties WHERE property_id = NEW.property_id) || ' expire le ' || NEW.end_date::TEXT,
                NEW.mandate_id,
                'mandate',
                CASE WHEN NEW.end_date < CURRENT_DATE THEN 'critical' ELSE 'warning' END
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Appliquer le trigger de conformité
CREATE TRIGGER compliance_property_documents
AFTER INSERT OR UPDATE ON property_documents
FOR EACH ROW EXECUTE FUNCTION check_compliance();

CREATE TRIGGER compliance_mandates
AFTER INSERT OR UPDATE ON mandates
FOR EACH ROW EXECUTE FUNCTION check_compliance();

-- =============================================
-- PROCÉDURES STOCKÉES
-- =============================================

-- Procédure pour générer des recommandations de biens par IA
CREATE OR REPLACE PROCEDURE generate_ai_recommendations(
    client_id UUID
)
LANGUAGE plpgsql
AS $$
DECLARE
    client_pref RECORD;
    property RECORD;
    match_score NUMERIC;
    total_properties INTEGER := 0;
    matched_properties INTEGER := 0;
BEGIN
    -- Récupérer les préférences du client
    SELECT * INTO client_pref
    FROM client_preferences
    WHERE client_id = generate_ai_recommendations.client_id
    ORDER BY updated_at DESC
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Aucune préférence trouvée pour le client';
    END IF;
    
    -- Supprimer les anciennes recommandations pour ce client
    DELETE FROM ai_recommendations WHERE client_id = generate_ai_recommendations.client_id;
    
    -- Parcourir tous les biens disponibles
    FOR property IN
        SELECT p.*, ps.is_available
        FROM properties p
        JOIN property_statuses ps ON p.status_id = ps.status_id
        WHERE ps.is_available = TRUE
    LOOP
        total_properties := total_properties + 1;
        
        -- Calculer le score de correspondance
        match_score := 0;
        
        -- Vérifier le prix
        IF client_pref.min_price IS NOT NULL AND property.price < client_pref.min_price THEN
            CONTINUE;
        END IF;
        IF client_pref.max_price IS NOT NULL AND property.price > client_pref.max_price THEN
            CONTINUE;
        END IF;
        
        -- Vérifier la surface
        IF client_pref.min_surface IS NOT NULL AND property.surface < client_pref.min_surface THEN
            CONTINUE;
        END IF;
        IF client_pref.max_surface IS NOT NULL AND property.surface > client_pref.max_surface THEN
            CONTINUE;
        END IF;
        
        -- Vérifier le nombre de pièces
        IF client_pref.min_rooms IS NOT NULL AND property.rooms < client_pref.min_rooms THEN
            CONTINUE;
        END IF;
        
        -- Vérifier le type de bien
        IF client_pref.property_type IS NOT NULL AND NOT (property.property_type_id::TEXT = ANY(client_pref.property_type)) THEN
            CONTINUE;
        END IF;
        
        -- Vérifier la localisation
        IF client_pref.locations IS NOT NULL AND NOT (property.city = ANY(client_pref.locations) OR property.postal_code = ANY(client_pref.locations)) THEN
            CONTINUE;
        END IF;
        
        -- Calculer le score de base
        match_score := 100;
        
        -- Ajuster le score en fonction de la proximité des critères
        IF client_pref.min_price IS NOT NULL AND client_pref.max_price IS NOT NULL THEN
            match_score := match_score - LEAST(ABS(property.price - client_pref.min_price), ABS(property.price - client_pref.max_price)) / GREATEST(client_pref.max_price - client_pref.min_price, 1) * 20;
        END IF;
        
        IF client_pref.min_surface IS NOT NULL AND client_pref.max_surface IS NOT NULL THEN
            match_score := match_score - LEAST(ABS(property.surface - client_pref.min_surface), ABS(property.surface - client_pref.max_surface)) / GREATEST(client_pref.max_surface - client_pref.min_surface, 1) * 15;
        END IF;
        
        -- Ajouter des points pour les caractéristiques souhaitées
        IF client_pref.other_criteria ? 'parking' AND client_pref.other_criteria->>'parking'::BOOLEAN = TRUE AND property.has_parking = TRUE THEN
            match_score := match_score + 5;
        END IF;
        IF client_pref.other_criteria ? 'elevator' AND client_pref.other_criteria->>'elevator'::BOOLEAN = TRUE AND property.has_elevator = TRUE THEN
            match_score := match_score + 5;
        END IF;
        IF client_pref.other_criteria ? 'garden' AND client_pref.other_criteria->>'garden'::BOOLEAN = TRUE AND property.has_garden = TRUE THEN
            match_score := match_score + 5;
        END IF;
        
        -- Limiter le score entre 0 et 100
        match_score := GREATEST(LEAST(match_score, 100), 0);
        
        -- Enregistrer la recommandation
        INSERT INTO ai_recommendations (client_id, property_id, score, reason)
        VALUES (
            generate_ai_recommendations.client_id,
            property.property_id,
            match_score,
            'Correspondance IA basée sur les préférences du client'
        );
        
        matched_properties := matched_properties + 1;
    END LOOP;
    
    RAISE NOTICE 'Généré % recommandations sur % biens disponibles pour le client %', matched_properties, total_properties, client_id;
END;
$$;

-- Procédure pour envoyer des relances automatiques
CREATE OR REPLACE PROCEDURE send_automated_reminders()
LANGUAGE plpgsql
AS $$
BEGIN
    -- Relances pour les visites programmées
    INSERT INTO interactions (client_id, user_id, interaction_type, notes, scheduled_for, completed_at)
    SELECT 
        t.client_id,
        (SELECT user_id FROM users LIMIT 1), -- Premier agent par défaut
        'relance_visite',
        'Rappel automatique pour la visite du bien ' || p.reference,
        v.scheduled_for - INTERVAL '24 hours',
        NULL
    FROM transactions t
    JOIN properties p ON t.property_id = p.property_id
    JOIN (
        SELECT 
            client_id, 
            property_id, 
            MIN(scheduled_for) AS scheduled_for
        FROM interactions 
        WHERE interaction_type = 'visite_programmee'
            AND scheduled_for > CURRENT_TIMESTAMP
            AND completed_at IS NULL
        GROUP BY client_id, property_id
    ) v ON t.client_id = v.client_id AND t.property_id = v.property_id
    WHERE v.scheduled_for - INTERVAL '24 hours' BETWEEN CURRENT_TIMESTAMP AND CURRENT_TIMESTAMP + INTERVAL '1 hour'
        AND NOT EXISTS (
            SELECT 1 FROM interactions 
            WHERE client_id = t.client_id 
                AND interaction_type = 'relance_visite'
                AND scheduled_for = v.scheduled_for - INTERVAL '24 hours'
        );
    
    -- Relances pour les paiements de loyer en retard
    INSERT INTO interactions (client_id, user_id, interaction_type, notes, scheduled_for, completed_at)
    SELECT 
        c.client_id,
        (SELECT user_id FROM users LIMIT 1),
        'relance_loyer',
        'Rappel de paiement de loyer pour le bien ' || p.reference,
        CURRENT_TIMESTAMP,
        NULL
    FROM rent_payments rp
    JOIN leases l ON rp.lease_id = l.lease_id
    JOIN transactions t ON l.transaction_id = t.transaction_id
    JOIN clients c ON t.client_id = c.client_id
    JOIN properties p ON t.property_id = p.property_id
    WHERE rp.is_paid = FALSE
        AND rp.due_date < CURRENT_DATE
        AND NOT EXISTS (
            SELECT 1 FROM interactions 
            WHERE client_id = c.client_id 
                AND interaction_type = 'relance_loyer'
                AND completed_at > rp.due_date
        );
    
    RAISE NOTICE 'Relances automatiques envoyées';
END;
$$;

-- =============================================
-- INDEXES POUR OPTIMISER LES REQUÊTES
-- =============================================

-- Index pour les recherches fréquentes
CREATE INDEX idx_properties_location ON properties USING GIN (city gin_trgm_ops, postal_code gin_trgm_ops);
CREATE INDEX idx_properties_price ON properties(price);
CREATE INDEX idx_properties_surface ON properties(surface);
CREATE INDEX idx_properties_rooms ON properties(rooms);
CREATE INDEX idx_clients_email ON clients USING HASH (email);
CREATE INDEX idx_clients_phone ON clients USING HASH (phone);
CREATE INDEX idx_interactions_client ON interactions(client_id);
CREATE INDEX idx_interactions_date ON interactions(scheduled_for);
CREATE INDEX idx_transactions_property ON transactions(property_id);
CREATE INDEX idx_transactions_client ON transactions(client_id);
CREATE INDEX idx_maintenance_requests_property ON maintenance_requests(property_id);
CREATE INDEX idx_maintenance_requests_status ON maintenance_requests(status_id);
CREATE INDEX idx_contracts_transaction ON contracts(transaction_id);
CREATE INDEX idx_invoices_client ON invoices(client_id);
CREATE INDEX idx_invoices_date ON invoices(issue_date);

-- Index pour les alertes
CREATE INDEX idx_alerts_unresolved ON alerts(is_resolved) WHERE is_resolved = FALSE;
CREATE INDEX idx_alerts_severity ON alerts(severity);

-- Index pour l'IA
CREATE INDEX idx_ai_recommendations_score ON ai_recommendations(score DESC);
CREATE INDEX idx_client_preferences_client ON client_preferences(client_id);

-- Index pour l'audit
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_table ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_date ON audit_logs(created_at DESC);

-- =============================================
-- CONFIGURATION DE L'APPLICATION
-- =============================================

-- Configuration des paramètres applicatifs
CREATE TABLE app_settings (
    setting_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_name VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO app_settings (setting_name, setting_value, description) VALUES
('company_name', 'Agence Immobilière Pro', 'Nom de l''agence'),
('company_address', '123 Avenue des Champs-Élysées, 75008 Paris', 'Adresse de l''agence'),
('default_commission_rate', '5.00', 'Taux de commission par défaut'),
('rent_index', 'IRL', 'Indice de référence pour les révisions de loyer'),
('min_lead_score_hot', '70', 'Score minimum pour considérer un lead comme "chaud"'),
('auto_reminders_enabled', 'true', 'Activer les relances automatiques'),
('compliance_check_frequency', 'daily', 'Fréquence des vérifications de conformité');

-- Configuration des rôles par défaut
INSERT INTO roles (name, description) VALUES
('agent', 'Agent immobilier - gestion des biens et clients'),
('comptable', 'Responsable comptable - gestion des finances'),
('admin', 'Administrateur système - gestion complète du système'),
('proprietaire', 'Propriétaire - accès limité à ses biens'),
('locataire', 'Locataire - accès limité à ses locations');

-- Attribution des permissions aux rôles
-- Agent
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'agent'
AND p.name IN (
    'property_view', 'property_create', 'property_edit',
    'client_view', 'client_create', 'client_edit',
    'transaction_view', 'transaction_edit',
    'invoice_create', 'invoice_view'
);

-- Comptable
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'comptable'
AND p.name IN (
    'invoice_view', 'report_view',
    'property_view', 'client_view'
);

-- Admin
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'admin'
AND p.name IN (
    SELECT name FROM permissions
);

-- Propriétaire
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'proprietaire'
AND p.name IN (
    'property_view', 'transaction_view', 'invoice_view'
);

-- Locataire
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.role_id, p.permission_id
FROM roles r
CROSS JOIN permissions p
WHERE r.name = 'locataire'
AND p.name IN (
    'transaction_view', 'invoice_view'
);