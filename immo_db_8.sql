-- =============================================================
--  Schema PostgreSQL — Application de gestion d’agence immobilière
--  Version unifiée (couverture CRM, biens, visites, transactions,
--  gestion locative, maintenance, marketing, finances, sécurité)
--  Auteur : ChatGPT
--  Compatibilité : PostgreSQL 14+
-- =============================================================

-- =====================
-- Préambule & extensions
-- =====================
CREATE SCHEMA IF NOT EXISTS re; -- "real estate"
SET search_path TO re, public;

-- Utilitaires (facultatifs)
CREATE EXTENSION IF NOT EXISTS pgcrypto; -- pour gen_random_uuid()

-- =====================
-- Types énumérés
-- =====================
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'contact_type') THEN
    CREATE TYPE contact_type AS ENUM ('buyer','seller','tenant','landlord','investor','partner','vendor','notary');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'lead_status') THEN
    CREATE TYPE lead_status AS ENUM ('new','working','qualified','unqualified','won','lost');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'property_type') THEN
    CREATE TYPE property_type AS ENUM ('apartment','house','land','commercial','office','industrial','newbuild');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'listing_status') THEN
    CREATE TYPE listing_status AS ENUM ('draft','published','under_offer','sold','let','withdrawn');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'mandate_type') THEN
    CREATE TYPE mandate_type AS ENUM ('exclusive','semi_exclusive','simple');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'visit_status') THEN
    CREATE TYPE visit_status AS ENUM ('scheduled','completed','cancelled','no_show');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'offer_status') THEN
    CREATE TYPE offer_status AS ENUM ('pending','accepted','rejected','withdrawn');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'transaction_type') THEN
    CREATE TYPE transaction_type AS ENUM ('sale','lease');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invoice_status') THEN
    CREATE TYPE invoice_status AS ENUM ('draft','issued','overdue','paid','cancelled');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payment_method') THEN
    CREATE TYPE payment_method AS ENUM ('card','transfer','sepa_dd','cash','cheque');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'maintenance_status') THEN
    CREATE TYPE maintenance_status AS ENUM ('open','assigned','in_progress','on_hold','completed','cancelled');
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'priority_level') THEN
    CREATE TYPE priority_level AS ENUM ('low','medium','high','urgent');
  END IF;
END $$;

-- =====================
-- Référentiel agences & utilisateurs
-- =====================
CREATE TABLE IF NOT EXISTS agencies (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name          TEXT NOT NULL,
  brand         TEXT,
  country       TEXT,
  currency      TEXT DEFAULT 'EUR',
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS branches (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  name          TEXT NOT NULL,
  address1      TEXT,
  address2      TEXT,
  city          TEXT,
  state         TEXT,
  postal_code   TEXT,
  country       TEXT,
  lat           NUMERIC(9,6),
  lon           NUMERIC(9,6),
  phone         TEXT,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS users (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  branch_id     UUID REFERENCES branches(id) ON DELETE SET NULL,
  email         CITEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  first_name    TEXT NOT NULL,
  last_name     TEXT NOT NULL,
  phone         TEXT,
  is_active     BOOLEAN NOT NULL DEFAULT TRUE,
  mfa_enabled   BOOLEAN NOT NULL DEFAULT FALSE,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS roles (
  id        SERIAL PRIMARY KEY,
  code      TEXT UNIQUE NOT NULL, -- e.g. admin, agent, manager, accountant
  label     TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS user_roles (
  user_id   UUID REFERENCES users(id) ON DELETE CASCADE,
  role_id   INT REFERENCES roles(id) ON DELETE CASCADE,
  PRIMARY KEY (user_id, role_id)
);

-- =====================
-- CRM / Contacts / Leads
-- =====================
CREATE TABLE IF NOT EXISTS contacts (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id       UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  contact_type    contact_type NOT NULL,
  first_name      TEXT,
  last_name       TEXT,
  company_name    TEXT,
  email           CITEXT,
  phone           TEXT,
  alt_phone       TEXT,
  address1        TEXT,
  address2        TEXT,
  city            TEXT,
  postal_code     TEXT,
  country         TEXT,
  preferences     JSONB,                -- critères de recherche, budget, localisation
  notes           TEXT,
  gdpr_consent    BOOLEAN DEFAULT FALSE,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_type ON contacts(contact_type);

CREATE TABLE IF NOT EXISTS contact_accounts (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contact_id      UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE,
  email           CITEXT UNIQUE NOT NULL,
  password_hash   TEXT NOT NULL,
  is_active       BOOLEAN NOT NULL DEFAULT TRUE,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS leads (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id       UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  contact_id      UUID REFERENCES contacts(id) ON DELETE SET NULL,
  assigned_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  source          TEXT,                -- web, portal, social, phone, walk-in, campaign
  status          lead_status NOT NULL DEFAULT 'new',
  score           INT CHECK (score BETWEEN 0 AND 100) DEFAULT 0,
  budget_min      NUMERIC(12,2),
  budget_max      NUMERIC(12,2),
  sector_pref     TEXT,
  data            JSONB,               -- payload libre (utm, etc.)
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_leads_status ON leads(status);
CREATE INDEX IF NOT EXISTS idx_leads_assigned ON leads(assigned_user_id);

CREATE TABLE IF NOT EXISTS interactions (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  contact_id    UUID REFERENCES contacts(id) ON DELETE SET NULL,
  lead_id       UUID REFERENCES leads(id) ON DELETE SET NULL,
  user_id       UUID REFERENCES users(id) ON DELETE SET NULL,
  channel       TEXT NOT NULL,        -- call, email, sms, whatsapp, meeting
  summary       TEXT,
  meta          JSONB,
  occurred_at   TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_interactions_contact ON interactions(contact_id);

-- =====================
-- Référentiel Biens / Listings / Mandats
-- =====================
CREATE TABLE IF NOT EXISTS properties (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id       UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  branch_id       UUID REFERENCES branches(id) ON DELETE SET NULL,
  owner_contact_id UUID REFERENCES contacts(id) ON DELETE SET NULL,
  property_type   property_type NOT NULL,
  title           TEXT NOT NULL,
  description     TEXT,
  address1        TEXT,
  address2        TEXT,
  city            TEXT,
  state           TEXT,
  postal_code     TEXT,
  country         TEXT,
  lat             NUMERIC(9,6),
  lon             NUMERIC(9,6),
  area_sqm        NUMERIC(10,2),
  rooms           INT,
  bedrooms        INT,
  bathrooms       INT,
  floor           INT,
  features        JSONB,      -- parking, ascenseur, jardin, etc.
  dpe_grade       TEXT,       -- A..G
  dpe_value       NUMERIC(6,2),
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_properties_type ON properties(property_type);
CREATE INDEX IF NOT EXISTS idx_properties_city ON properties(city);

CREATE TABLE IF NOT EXISTS property_documents (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  category      TEXT NOT NULL, -- dpe, title_deed, plan, photo, video, etc.
  storage_url   TEXT NOT NULL,
  mime_type     TEXT,
  version       INT NOT NULL DEFAULT 1,
  uploaded_by   UUID REFERENCES users(id) ON DELETE SET NULL,
  uploaded_at   TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS property_media (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  kind          TEXT NOT NULL, -- photo, video, tour_360, plan
  storage_url   TEXT NOT NULL,
  sort_order    INT DEFAULT 0,
  caption       TEXT
);

CREATE TABLE IF NOT EXISTS mandates (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  type          mandate_type NOT NULL,
  commission_rate NUMERIC(5,2), -- %, peut être null si forfait
  commission_flat NUMERIC(12,2),
  start_date    DATE NOT NULL,
  end_date      DATE,
  signed_doc_id UUID REFERENCES property_documents(id) ON DELETE SET NULL,
  notes         TEXT
);
CREATE INDEX IF NOT EXISTS idx_mandates_property ON mandates(property_id);

CREATE TABLE IF NOT EXISTS portals (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name        TEXT UNIQUE NOT NULL
);

CREATE TABLE IF NOT EXISTS listings (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  mandate_id    UUID REFERENCES mandates(id) ON DELETE SET NULL,
  status        listing_status NOT NULL DEFAULT 'draft',
  price         NUMERIC(12,2), -- prix de vente
  rent          NUMERIC(12,2), -- loyer mensuel
  charges       NUMERIC(12,2),
  currency      TEXT DEFAULT 'EUR',
  seo_title     TEXT,
  seo_slug      TEXT,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_listings_status ON listings(status);

CREATE TABLE IF NOT EXISTS listing_portal_publications (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  listing_id    UUID NOT NULL REFERENCES listings(id) ON DELETE CASCADE,
  portal_id     UUID NOT NULL REFERENCES portals(id) ON DELETE CASCADE,
  external_id   TEXT,
  published_at  TIMESTAMPTZ,
  status        TEXT, -- published, failed, removed
  details       JSONB
);

CREATE TABLE IF NOT EXISTS price_history (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  effective_at  DATE NOT NULL,
  price         NUMERIC(12,2),
  rent          NUMERIC(12,2),
  notes         TEXT
);

-- =====================
-- Visites / Agenda / Feedback
-- =====================
CREATE TABLE IF NOT EXISTS appointments (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  property_id   UUID REFERENCES properties(id) ON DELETE SET NULL,
  contact_id    UUID REFERENCES contacts(id) ON DELETE SET NULL,
  user_id       UUID REFERENCES users(id) ON DELETE SET NULL,
  starts_at     TIMESTAMPTZ NOT NULL,
  ends_at       TIMESTAMPTZ NOT NULL,
  status        visit_status NOT NULL DEFAULT 'scheduled',
  location      TEXT,
  notes         TEXT,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_appointments_user_time ON appointments(user_id, starts_at);

CREATE TABLE IF NOT EXISTS visit_feedback (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  interest_score INT CHECK (interest_score BETWEEN 1 AND 5),
  comment       TEXT,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- =====================
-- Offres / Transactions Vente & Location
-- =====================
CREATE TABLE IF NOT EXISTS offers (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  contact_id    UUID NOT NULL REFERENCES contacts(id) ON DELETE CASCADE, -- acheteur/locataire
  type          transaction_type NOT NULL,
  amount        NUMERIC(12,2) NOT NULL,
  status        offer_status NOT NULL DEFAULT 'pending',
  message       TEXT,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_offers_property ON offers(property_id);

CREATE TABLE IF NOT EXISTS sales (
  id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id     UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  buyer_id        UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
  seller_id       UUID REFERENCES contacts(id) ON DELETE SET NULL,
  offer_id        UUID REFERENCES offers(id) ON DELETE SET NULL,
  mandate_id      UUID REFERENCES mandates(id) ON DELETE SET NULL,
  notary_id       UUID REFERENCES contacts(id) ON DELETE SET NULL, -- notaire
  status          TEXT NOT NULL DEFAULT 'in_progress', -- in_progress, signed, closed, cancelled
  compromise_at   DATE,
  deed_at         DATE,
  sale_price      NUMERIC(12,2) NOT NULL,
  notes           TEXT,
  created_at      TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS leases (
  id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id       UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  tenant_id         UUID NOT NULL REFERENCES contacts(id) ON DELETE RESTRICT,
  landlord_id       UUID REFERENCES contacts(id) ON DELETE SET NULL,
  start_date        DATE NOT NULL,
  end_date          DATE,
  rent              NUMERIC(12,2) NOT NULL,
  charges           NUMERIC(12,2) DEFAULT 0,
  deposit_amount    NUMERIC(12,2) DEFAULT 0,
  index_reference   TEXT, -- ex: IRL
  status            TEXT NOT NULL DEFAULT 'active', -- active, terminated, expired
  created_at        TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_leases_property ON leases(property_id);

-- =====================
-- Facturation / Paiements / Commissions
-- =====================
CREATE TABLE IF NOT EXISTS invoices (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  related_type  transaction_type, -- sale|lease ou NULL pour autres
  related_id    UUID,             -- sales.id ou leases.id
  contact_id    UUID REFERENCES contacts(id) ON DELETE SET NULL, -- débiteur
  issue_date    DATE NOT NULL DEFAULT CURRENT_DATE,
  due_date      DATE NOT NULL,
  status        invoice_status NOT NULL DEFAULT 'issued',
  currency      TEXT NOT NULL DEFAULT 'EUR',
  subtotal      NUMERIC(12,2) NOT NULL DEFAULT 0,
  tax_rate      NUMERIC(5,2) NOT NULL DEFAULT 0,
  total         NUMERIC(12,2) GENERATED ALWAYS AS (round(subtotal * (1 + tax_rate/100),2)) STORED,
  meta          JSONB
);

CREATE TABLE IF NOT EXISTS invoice_lines (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id    UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  description   TEXT NOT NULL,
  qty           NUMERIC(12,2) NOT NULL DEFAULT 1,
  unit_price    NUMERIC(12,2) NOT NULL DEFAULT 0,
  line_total    NUMERIC(12,2) GENERATED ALWAYS AS (round(qty * unit_price,2)) STORED
);

CREATE TABLE IF NOT EXISTS payments (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id    UUID NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
  method        payment_method NOT NULL,
  amount        NUMERIC(12,2) NOT NULL,
  received_at   TIMESTAMPTZ NOT NULL DEFAULT now(),
  reference     TEXT,
  meta          JSONB
);

CREATE TABLE IF NOT EXISTS commissions (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  user_id       UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  transaction_type transaction_type NOT NULL,
  transaction_id  UUID NOT NULL, -- sales.id ou leases.id
  percentage    NUMERIC(5,2) NOT NULL,
  base_amount   NUMERIC(12,2) NOT NULL,
  amount        NUMERIC(12,2) NOT NULL,
  status        TEXT NOT NULL DEFAULT 'pending' -- pending, approved, paid
);
CREATE INDEX IF NOT EXISTS idx_commissions_user ON commissions(user_id);

-- =====================
-- Gestion locative détaillée (quittances, indexation, régul charges)
-- =====================
CREATE TABLE IF NOT EXISTS rent_indices (
  id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reference   TEXT NOT NULL,   -- ex: IRL
  period_date DATE NOT NULL,   -- date de référence (trimestre)
  value       NUMERIC(8,4) NOT NULL,
  UNIQUE(reference, period_date)
);

CREATE TABLE IF NOT EXISTS rent_invoices (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lease_id      UUID NOT NULL REFERENCES leases(id) ON DELETE CASCADE,
  invoice_id    UUID UNIQUE REFERENCES invoices(id) ON DELETE SET NULL,
  period_start  DATE NOT NULL,
  period_end    DATE NOT NULL,
  rent_amount   NUMERIC(12,2) NOT NULL,
  charges_amount NUMERIC(12,2) NOT NULL DEFAULT 0,
  generated_at  TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(lease_id, period_start, period_end)
);

-- =====================
-- Maintenance / Fournisseurs / Tickets
-- =====================
CREATE TABLE IF NOT EXISTS vendors (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  name          TEXT NOT NULL,
  email         CITEXT,
  phone         TEXT,
  specialty     TEXT,
  address       TEXT,
  created_at    TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS maintenance_tickets (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id   UUID NOT NULL REFERENCES properties(id) ON DELETE CASCADE,
  reporter_id   UUID REFERENCES contacts(id) ON DELETE SET NULL,
  assigned_vendor_id UUID REFERENCES vendors(id) ON DELETE SET NULL,
  title         TEXT NOT NULL,
  description   TEXT,
  priority      priority_level NOT NULL DEFAULT 'medium',
  status        maintenance_status NOT NULL DEFAULT 'open',
  opened_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
  closed_at     TIMESTAMPTZ
);

CREATE TABLE IF NOT EXISTS work_orders (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  ticket_id     UUID NOT NULL REFERENCES maintenance_tickets(id) ON DELETE CASCADE,
  vendor_id     UUID REFERENCES vendors(id) ON DELETE SET NULL,
  scheduled_at  TIMESTAMPTZ,
  cost_estimate NUMERIC(12,2),
  cost_final    NUMERIC(12,2),
  status        TEXT NOT NULL DEFAULT 'open'
);

-- =====================
-- Marketing / Campagnes / Pixels
-- =====================
CREATE TABLE IF NOT EXISTS campaigns (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  name          TEXT NOT NULL,
  channel       TEXT NOT NULL, -- email, sms, ads, social
  starts_at     TIMESTAMPTZ,
  ends_at       TIMESTAMPTZ,
  budget        NUMERIC(12,2),
  meta          JSONB
);

CREATE TABLE IF NOT EXISTS campaign_stats (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id   UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
  metric        TEXT NOT NULL, -- impressions, clicks, ctr, conversions
  value         NUMERIC(18,4) NOT NULL,
  captured_at   TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- =====================
-- Notifications / Templates
-- =====================
CREATE TABLE IF NOT EXISTS notification_templates (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  code          TEXT UNIQUE NOT NULL, -- e.g. VISIT_REMINDER
  channel       TEXT NOT NULL, -- email, sms, push
  subject       TEXT,
  body_md       TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS notifications (
  id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agency_id     UUID NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
  contact_id    UUID REFERENCES contacts(id) ON DELETE SET NULL,
  user_id       UUID REFERENCES users(id) ON DELETE SET NULL,
  template_id   UUID REFERENCES notification_templates(id) ON DELETE SET NULL,
  channel       TEXT NOT NULL,
  payload       JSONB,
  scheduled_at  TIMESTAMPTZ,
  sent_at       TIMESTAMPTZ,
  status        TEXT DEFAULT 'pending'
);

-- =====================
-- Audit & Sécurité
-- =====================
CREATE TABLE IF NOT EXISTS audit_log (
  id            BIGSERIAL PRIMARY KEY,
  table_name    TEXT NOT NULL,
  action        TEXT NOT NULL, -- INSERT, UPDATE, DELETE
  row_pk        TEXT,
  changed_at    TIMESTAMPTZ NOT NULL DEFAULT now(),
  changed_by    UUID,
  old_data      JSONB,
  new_data      JSONB
);

-- Fonction d’audit générique
CREATE OR REPLACE FUNCTION fn_audit_trigger()
RETURNS TRIGGER AS $$
DECLARE
  v_pk TEXT;
BEGIN
  -- essaie de lire la PK (suppose une colonne id)
  IF TG_OP = 'INSERT' THEN
    v_pk := COALESCE(NEW.id::text, NULL);
    INSERT INTO audit_log(table_name, action, row_pk, changed_by, old_data, new_data)
    VALUES (TG_TABLE_NAME, TG_OP, v_pk, current_setting('app.user_id', true)::uuid, NULL, to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' THEN
    v_pk := COALESCE(NEW.id::text, OLD.id::text);
    INSERT INTO audit_log(table_name, action, row_pk, changed_by, old_data, new_data)
    VALUES (TG_TABLE_NAME, TG_OP, v_pk, current_setting('app.user_id', true)::uuid, to_jsonb(OLD), to_jsonb(NEW));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    v_pk := COALESCE(OLD.id::text, NULL);
    INSERT INTO audit_log(table_name, action, row_pk, changed_by, old_data, new_data)
    VALUES (TG_TABLE_NAME, TG_OP, v_pk, current_setting('app.user_id', true)::uuid, to_jsonb(OLD), NULL);
    RETURN OLD;
  END IF;
  RETURN NULL;
END; $$ LANGUAGE plpgsql;

-- Attache l’audit aux tables critiques
DO $$
DECLARE r RECORD;
BEGIN
  FOR r IN SELECT relname FROM pg_class c JOIN pg_namespace n ON n.oid=c.relnamespace
           WHERE n.nspname='re' AND c.relkind='r' AND relname IN (
             'contacts','leads','properties','listings','mandates','offers','sales','leases',
             'invoices','payments','commissions','maintenance_tickets')
  LOOP
    EXECUTE format('DROP TRIGGER IF EXISTS trg_audit_%I ON re.%I;', r.relname, r.relname);
    EXECUTE format('CREATE TRIGGER trg_audit_%I AFTER INSERT OR UPDATE OR DELETE ON re.%I
                    FOR EACH ROW EXECUTE FUNCTION fn_audit_trigger();', r.relname, r.relname);
  END LOOP;
END $$;

-- =====================
-- Fonctions métier
-- =====================
-- 1) Calcul de commission pour une vente ou un bail
CREATE OR REPLACE FUNCTION fn_calculate_commission(
  p_transaction_type transaction_type,
  p_transaction_id UUID,
  p_percentage NUMERIC
) RETURNS NUMERIC AS $$
DECLARE
  v_base NUMERIC := 0;
BEGIN
  IF p_transaction_type = 'sale' THEN
    SELECT sale_price INTO v_base FROM sales WHERE id = p_transaction_id;
  ELSIF p_transaction_type = 'lease' THEN
    -- base = 1 mois de loyer par défaut (exemple)
    SELECT rent INTO v_base FROM leases WHERE id = p_transaction_id;
  END IF;
  IF v_base IS NULL THEN RAISE EXCEPTION 'Transaction introuvable'; END IF;
  RETURN round(v_base * p_percentage/100.0, 2);
END; $$ LANGUAGE plpgsql;

-- 2) Mise à jour du statut de listing / propriété après offre acceptée
CREATE OR REPLACE FUNCTION fn_after_offer_update()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'accepted' THEN
    UPDATE listings SET status = 'under_offer', updated_at=now()
     WHERE property_id = NEW.property_id AND status IN ('published');
  END IF;
  RETURN NEW;
END; $$ LANGUAGE plpgsql;

CREATE TRIGGER trg_offer_status
AFTER UPDATE ON offers
FOR EACH ROW WHEN (OLD.status IS DISTINCT FROM NEW.status)
EXECUTE FUNCTION fn_after_offer_update();

-- 3) Mise à jour automatique du statut de propriété après vente/bail
CREATE OR REPLACE FUNCTION fn_sync_property_status()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_TABLE_NAME = 'sales' THEN
    IF NEW.status IN ('signed','closed') THEN
      UPDATE listings SET status='sold', updated_at=now() WHERE property_id = NEW.property_id;
    END IF;
  ELSIF TG_TABLE_NAME = 'leases' THEN
    IF NEW.status = 'active' THEN
      UPDATE listings SET status='let', updated_at=now() WHERE property_id = NEW.property_id;
    ELSIF NEW.status IN ('terminated','expired') THEN
      UPDATE listings SET status='published', updated_at=now() WHERE property_id = NEW.property_id;
    END IF;
  END IF;
  RETURN NEW;
END; $$ LANGUAGE plpgsql;

CREATE TRIGGER trg_sales_status
AFTER INSERT OR UPDATE ON sales
FOR EACH ROW EXECUTE FUNCTION fn_sync_property_status();

CREATE TRIGGER trg_leases_status
AFTER INSERT OR UPDATE ON leases
FOR EACH ROW EXECUTE FUNCTION fn_sync_property_status();

-- 4) Mise à jour automatique du statut de facture selon paiements
CREATE OR REPLACE FUNCTION fn_update_invoice_status()
RETURNS TRIGGER AS $$
DECLARE v_paid NUMERIC; v_total NUMERIC;
BEGIN
  SELECT COALESCE(SUM(amount),0) INTO v_paid FROM payments WHERE invoice_id = NEW.invoice_id;
  SELECT total INTO v_total FROM invoices WHERE id = NEW.invoice_id;
  IF v_paid >= v_total THEN
    UPDATE invoices SET status='paid' WHERE id = NEW.invoice_id;
  ELSIF v_paid > 0 THEN
    UPDATE invoices SET status='issued' WHERE id = NEW.invoice_id;
  END IF;
  RETURN NEW;
END; $$ LANGUAGE plpgsql;

CREATE TRIGGER trg_payment_after
AFTER INSERT OR UPDATE ON payments
FOR EACH ROW EXECUTE FUNCTION fn_update_invoice_status();

-- 5) Indexation de loyer : calcule le nouveau loyer basé sur indice
CREATE OR REPLACE FUNCTION fn_index_rent(p_lease_id UUID, p_period DATE)
RETURNS NUMERIC AS $$
DECLARE
  v_lease leases%ROWTYPE;
  v_last rent_indices.value%TYPE;
  v_new  rent_indices.value%TYPE;
  v_new_rent NUMERIC;
BEGIN
  SELECT * INTO v_lease FROM leases WHERE id = p_lease_id;
  IF v_lease IS NULL THEN RAISE EXCEPTION 'Bail introuvable'; END IF;
  IF v_lease.index_reference IS NULL THEN RETURN v_lease.rent; END IF;

  SELECT value INTO v_last FROM rent_indices
    WHERE reference=v_lease.index_reference AND period_date=(
      SELECT max(period_date) FROM rent_indices WHERE reference=v_lease.index_reference AND period_date<=v_lease.start_date);

  SELECT value INTO v_new FROM rent_indices
    WHERE reference=v_lease.index_reference AND period_date=(
      SELECT max(period_date) FROM rent_indices WHERE reference=v_lease.index_reference AND period_date<=p_period);

  IF v_last IS NULL OR v_new IS NULL THEN RETURN v_lease.rent; END IF;
  v_new_rent := round(v_lease.rent * (v_new / v_last), 2);
  RETURN v_new_rent;
END; $$ LANGUAGE plpgsql;

-- 6) Procédure : Générer les factures de loyer pour une période
CREATE OR REPLACE PROCEDURE proc_generate_rent_invoices(p_lease_id UUID, p_start DATE, p_end DATE, p_tax NUMERIC DEFAULT 0)
LANGUAGE plpgsql AS $$
DECLARE
  v_lease leases%ROWTYPE;
  v_rent NUMERIC;
  v_inv_id UUID;
BEGIN
  SELECT * INTO v_lease FROM leases WHERE id = p_lease_id;
  IF NOT FOUND THEN RAISE EXCEPTION 'Bail introuvable'; END IF;

  v_rent := fn_index_rent(p_lease_id, p_start);

  INSERT INTO invoices(agency_id, related_type, related_id, contact_id, issue_date, due_date, status, currency, subtotal, tax_rate)
  VALUES ( (SELECT agency_id FROM properties WHERE id=v_lease.property_id), 'lease', p_lease_id, v_lease.tenant_id,
           p_start, p_end, 'issued', 'EUR', v_rent + COALESCE(v_lease.charges,0), p_tax)
  RETURNING id INTO v_inv_id;

  INSERT INTO invoice_lines(invoice_id, description, qty, unit_price)
  VALUES (v_inv_id, 'Loyer', 1, v_rent),
         (v_inv_id, 'Charges', 1, COALESCE(v_lease.charges,0));

  INSERT INTO rent_invoices(lease_id, invoice_id, period_start, period_end, rent_amount, charges_amount)
  VALUES (p_lease_id, v_inv_id, p_start, p_end, v_rent, COALESCE(v_lease.charges,0));
END; $$;

-- 7) Fonction simple de matching (biens ↔ préférences contact)
CREATE OR REPLACE FUNCTION fn_match_properties(p_contact_id UUID, p_limit INT DEFAULT 20)
RETURNS TABLE(property_id UUID, score NUMERIC) AS $$
DECLARE v_prefs JSONB; v_city TEXT; v_min NUMERIC; v_max NUMERIC; v_type property_type;
BEGIN
  SELECT preferences INTO v_prefs FROM contacts WHERE id = p_contact_id;
  v_city := COALESCE(v_prefs->>'city', NULL);
  v_min := NULLIF(v_prefs->>'budget_min','')::NUMERIC;
  v_max := NULLIF(v_prefs->>'budget_max','')::NUMERIC;
  v_type := NULLIF(v_prefs->>'property_type','')::property_type;

  RETURN QUERY
  SELECT p.id, (
      (CASE WHEN v_city IS NULL OR lower(p.city)=lower(v_city) THEN 1 ELSE 0 END) +
      (CASE WHEN v_type IS NULL OR p.property_type=v_type THEN 1 ELSE 0 END) +
      (CASE WHEN v_min IS NULL OR (l.price IS NOT NULL AND l.price>=v_min) OR (l.rent IS NOT NULL AND l.rent>=v_min) THEN 1 ELSE 0 END) +
      (CASE WHEN v_max IS NULL OR (l.price IS NOT NULL AND l.price<=v_max) OR (l.rent IS NOT NULL AND l.rent<=v_max) THEN 1 ELSE 0 END)
    )::NUMERIC AS score
  FROM properties p
  JOIN listings l ON l.property_id=p.id AND l.status IN ('published')
  ORDER BY score DESC
  LIMIT p_limit;
END; $$ LANGUAGE plpgsql;

-- =====================
-- Vues analytiques (exemples)
-- =====================
-- 1) Performance agent (pipeline simple)
CREATE OR REPLACE VIEW vw_agent_performance AS
SELECT u.id AS agent_id,
       u.first_name || ' ' || u.last_name AS agent_name,
       COUNT(DISTINCT ld.id) FILTER (WHERE ld.assigned_user_id=u.id) AS leads_assigned,
       COUNT(DISTINCT ap.id) FILTER (WHERE ap.user_id=u.id) AS visits_done,
       COUNT(DISTINCT s.id)  AS sales_count,
       SUM(s.sale_price)     AS sales_volume,
       COUNT(DISTINCT lz.id) AS leases_count
FROM users u
LEFT JOIN leads ld ON ld.assigned_user_id=u.id
LEFT JOIN appointments ap ON ap.user_id=u.id AND ap.status='completed'
LEFT JOIN sales s ON s.property_id IN (SELECT id FROM properties WHERE agency_id=u.agency_id)
LEFT JOIN leases lz ON lz.property_id IN (SELECT id FROM properties WHERE agency_id=u.agency_id)
GROUP BY u.id, agent_name;

-- 2) Taux de conversion des listings
CREATE OR REPLACE VIEW vw_listing_conversion AS
SELECT l.id AS listing_id, l.status,
       COUNT(DISTINCT ap.id) AS visits,
       COUNT(DISTINCT ofr.id) FILTER (WHERE ofr.status='accepted') AS offers_accepted,
       CASE WHEN COUNT(DISTINCT ap.id)=0 THEN 0
            ELSE ROUND( (COUNT(DISTINCT ofr.id) FILTER (WHERE ofr.status='accepted')::NUMERIC
                        / COUNT(DISTINCT ap.id)) * 100, 2) END AS visit_to_offer_rate
FROM listings l
LEFT JOIN appointments ap ON ap.property_id = l.property_id AND ap.status='completed'
LEFT JOIN offers ofr ON ofr.property_id = l.property_id
GROUP BY l.id, l.status;

-- 3) Rent roll courant
CREATE OR REPLACE VIEW vw_rent_roll AS
SELECT le.id AS lease_id, p.title AS property, c.first_name||' '||c.last_name AS tenant,
       le.rent, le.charges, (le.rent+le.charges) AS monthly_total, le.start_date, le.end_date, le.status
FROM leases le
JOIN properties p ON p.id=le.property_id
JOIN contacts c ON c.id=le.tenant_id
WHERE le.status='active';

-- 4) Arriérés locatifs
CREATE OR REPLACE VIEW vw_lease_arrears AS
SELECT le.id AS lease_id,
       SUM(inv.total) - COALESCE(SUM(pay.amount),0) AS balance_due
FROM leases le
JOIN rent_invoices ri ON ri.lease_id=le.id
JOIN invoices inv ON inv.id=ri.invoice_id AND inv.status IN ('issued','overdue')
LEFT JOIN payments pay ON pay.invoice_id=inv.id
GROUP BY le.id
HAVING (SUM(inv.total) - COALESCE(SUM(pay.amount),0)) > 0;

-- =====================
-- Règles de sécurité (exemple RLS sur contacts)
-- =====================
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Politique simple : un utilisateur ne voit que les contacts de son agence
CREATE POLICY contacts_agency_isolation ON contacts
  USING (agency_id = (SELECT agency_id FROM users WHERE id = current_setting('app.user_id', true)::uuid));

-- =====================
-- Index utiles
-- =====================
CREATE INDEX IF NOT EXISTS idx_properties_geo ON properties(lat, lon);
CREATE INDEX IF NOT EXISTS idx_listings_property ON listings(property_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_payments_invoice ON payments(invoice_id);

-- =====================
-- Données de démonstration minimales (optionnelles)
-- =====================
INSERT INTO roles(code,label) VALUES
  ('admin','Administrator'),('agent','Agent'),('manager','Manager'),('accountant','Accountant')
ON CONFLICT (code) DO NOTHING;

-- =====================
-- Notes d’usage
-- =====================
-- 1) Pour tracer l’utilisateur courant dans l’audit, définir :
--    SELECT set_config('app.user_id', '<uuid_user>', false);
-- 2) Générer une facture de loyer :
--    CALL proc_generate_rent_invoices('<lease_uuid>','2025-09-01','2025-09-30',20);
-- 3) Calculer une commission :
--    SELECT fn_calculate_commission('sale','<sale_uuid>', 5.0);
-- 4) Faire un matching de biens pour un contact :
--    SELECT * FROM fn_match_properties('<contact_uuid>', 10);
-- =============================================================

