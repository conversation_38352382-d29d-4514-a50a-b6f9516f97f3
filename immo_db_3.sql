-- Création des extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =============================================
-- TABLES PRINCIPALES
-- =============================================

-- Types de biens immobiliers
CREATE TABLE type_bien (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Statuts des biens
CREATE TABLE statut_bien (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    couleur VARCHAR(7) DEFAULT '#3498db'
);

-- Types de mandats
CREATE TABLE type_mandat (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Types de documents
CREATE TABLE type_document (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    categorie VARCHAR(50) NOT NULL
);

-- Types d'intervenants
CREATE TABLE type_intervenant (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Rôles utilisateurs
CREATE TABLE role (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Statuts des transactions
CREATE TABLE statut_transaction (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    couleur VARCHAR(7) DEFAULT '#2ecc71'
);

-- Types de communications
CREATE TABLE type_communication (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    canal VARCHAR(20) NOT NULL
);

-- Types de campagnes marketing
CREATE TABLE type_campagne (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Types de tâches
CREATE TABLE type_tache (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- Types de paiements
CREATE TABLE type_paiement (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(50) NOT NULL UNIQUE,
    description TEXT
);

-- =============================================
-- TABLES DE DONNÉES
-- =============================================

-- Utilisateurs (agents, administrateurs)
CREATE TABLE utilisateur (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    mot_de_passe VARCHAR(255) NOT NULL,
    telephone VARCHAR(20),
    photo_url TEXT,
    date_creation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    derniere_connexion TIMESTAMP WITH TIME ZONE,
    actif BOOLEAN DEFAULT true,
    id_role INTEGER REFERENCES role(id),
    id_agence INTEGER,
    CONSTRAINT chk_email CHECK (email ~* '^[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+[.][A-Za-z]+$')
);

-- Clients (acquéreurs, vendeurs, locataires, propriétaires)
CREATE TABLE client (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    telephone VARCHAR(20),
    adresse TEXT,
    code_postal VARCHAR(10),
    ville VARCHAR(100),
    pays VARCHAR(50) DEFAULT 'France',
    type_client VARCHAR(20) NOT NULL CHECK (type_client IN ('acquéreur', 'vendeur', 'locataire', 'propriétaire', 'investisseur')),
    date_creation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    derniere_interaction TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    source_lead VARCHAR(50),
    budget DECIMAL(12,2),
    criteres_recherche JSONB,
    id_agent_charge UUID REFERENCES utilisateur(id)
);

-- Biens immobiliers
CREATE TABLE bien (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(20) UNIQUE,
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    type_bien_id INTEGER REFERENCES type_bien(id),
    statut_id INTEGER REFERENCES statut_bien(id),
    adresse TEXT NOT NULL,
    code_postal VARCHAR(10) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    pays VARCHAR(50) DEFAULT 'France',
    prix DECIMAL(12,2) NOT NULL,
    surface_habitable DECIMAL(8,2),
    surface_terrain DECIMAL(8,2),
    nb_pieces INTEGER,
    nb_chambres INTEGER,
    nb_salles_bain INTEGER,
    annee_construction INTEGER,
    etat_general VARCHAR(50),
    dpe VARCHAR(5),
    ges VARCHAR(5),
    charges_mensuelles DECIMAL(8,2),
    taxe_fonciere DECIMAL(8,2),
    date_creation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date_mise_en_ligne TIMESTAMP WITH TIME ZONE,
    date_vente TIMESTAMP WITH TIME ZONE,
    id_proprietaire UUID REFERENCES client(id),
    id_agent_charge UUID REFERENCES utilisateur(id),
    geoloc POINT,
    caracteristiques_supplementaires JSONB,
    CONSTRAINT chk_surface CHECK (surface_habitable > 0 AND surface_terrain >= 0)
);

-- Mandats
CREATE TABLE mandat (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(20) UNIQUE,
    type_mandat_id INTEGER REFERENCES type_mandat(id),
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    prix_mandat DECIMAL(12,2),
    commission_taux DECIMAL(5,2),
    conditions_particulieres TEXT,
    date_signature DATE,
    id_bien UUID REFERENCES bien(id) NOT NULL,
    id_proprietaire UUID REFERENCES client(id) NOT NULL,
    id_agent_charge UUID REFERENCES utilisateur(id),
    actif BOOLEAN DEFAULT true
);

-- Transactions
CREATE TABLE transaction (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(20) UNIQUE,
    type_transaction VARCHAR(20) NOT NULL CHECK (type_transaction IN ('vente', 'location', 'location_saisonniere')),
    prix_final DECIMAL(12,2),
    date_signature TIMESTAMP WITH TIME ZONE,
    date_effet TIMESTAMP WITH TIME ZONE,
    date_fin_prevue TIMESTAMP WITH TIME ZONE,
    statut_id INTEGER REFERENCES statut_transaction(id),
    id_bien UUID REFERENCES bien(id) NOT NULL,
    id_acheteur UUID REFERENCES client(id),
    id_vendeur UUID REFERENCES client(id),
    id_agent_charge UUID REFERENCES utilisateur(id),
    notes TEXT
);

-- Documents
CREATE TABLE document (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom_fichier VARCHAR(255) NOT NULL,
    type_fichier VARCHAR(50) NOT NULL,
    taille_octets INTEGER,
    chemin_stockage TEXT NOT NULL,
    date_upload TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    type_document_id INTEGER REFERENCES type_document(id),
    id_bien UUID REFERENCES bien(id),
    id_client UUID REFERENCES client(id),
    id_transaction UUID REFERENCES transaction(id),
    id_mandat UUID REFERENCES mandat(id),
    id_utilisateur UUID REFERENCES utilisateur(id)
);

-- Visites
CREATE TABLE visite (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date_visite TIMESTAMP WITH TIME ZONE NOT NULL,
    duree_minutes INTEGER DEFAULT 30,
    statut VARCHAR(20) DEFAULT 'planifiee' CHECK (statut IN ('planifiee', 'confirmee', 'effectuee', 'annulee')),
    notes_agent TEXT,
    feedback_client TEXT,
    note_client INTEGER CHECK (note_client BETWEEN 1 AND 5),
    id_bien UUID REFERENCES bien(id) NOT NULL,
    id_client UUID REFERENCES client(id) NOT NULL,
    id_agent UUID REFERENCES utilisateur(id) NOT NULL
);

-- Offres
CREATE TABLE offre (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    montant DECIMAL(12,2) NOT NULL,
    date_offre TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date_limite_reponse DATE,
    conditions TEXT,
    statut VARCHAR(20) DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'acceptee', 'refusee', 'retiree')),
    id_bien UUID REFERENCES bien(id) NOT NULL,
    id_client UUID REFERENCES client(id) NOT NULL,
    id_agent_charge UUID REFERENCES utilisateur(id),
    id_transaction UUID REFERENCES transaction(id)
);

-- Contrats
CREATE TABLE contrat (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(20) UNIQUE,
    type_contrat VARCHAR(50) NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE,
    montant_mensuel DECIMAL(10,2),
    depot_garantie DECIMAL(10,2),
    conditions_particulieres TEXT,
    date_signature DATE,
    id_bien UUID REFERENCES bien(id) NOT NULL,
    id_proprietaire UUID REFERENCES client(id) NOT NULL,
    id_locataire UUID REFERENCES client(id),
    id_transaction UUID REFERENCES transaction(id),
    id_agent_charge UUID REFERENCES utilisateur(id)
);

-- Paiements
CREATE TABLE paiement (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference VARCHAR(30) UNIQUE,
    montant DECIMAL(12,2) NOT NULL,
    date_paiement TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date_echeance DATE,
    statut VARCHAR(20) DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'effectue', 'retard', 'annule')),
    type_paiement_id INTEGER REFERENCES type_paiement(id),
    id_client UUID REFERENCES client(id),
    id_transaction UUID REFERENCES transaction(id),
    id_contrat UUID REFERENCES contrat(id),
    id_agent_beneficiaire UUID REFERENCES utilisateur(id),
    notes TEXT
);

-- Intervenants externes
CREATE TABLE intervenant (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(100) NOT NULL,
    raison_sociale VARCHAR(100),
    email VARCHAR(255),
    telephone VARCHAR(20),
    adresse TEXT,
    specialite VARCHAR(100),
    type_intervenant_id INTEGER REFERENCES type_intervenant(id),
    notes TEXT,
    date_creation TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Interventions (maintenance, diagnostics, etc.)
CREATE TABLE intervention (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    description TEXT NOT NULL,
    date_demande TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date_intervention_prevue TIMESTAMP WITH TIME ZONE,
    date_intervention_reelle TIMESTAMP WITH TIME ZONE,
    statut VARCHAR(20) DEFAULT 'demandee' CHECK (statut IN ('demandee', 'planifiee', 'en_cours', 'terminee', 'annulee')),
    cout_previsionnel DECIMAL(10,2),
    cout_reel DECIMAL(10,2),
    id_bien UUID REFERENCES bien(id),
    id_intervenant UUID REFERENCES intervenant(id),
    id_agent_charge UUID REFERENCES utilisateur(id),
    id_contrat UUID REFERENCES contrat(id)
);

-- Litiges
CREATE TABLE litige (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sujet VARCHAR(255) NOT NULL,
    description TEXT,
    date_ouverture TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date_cloture TIMESTAMP WITH TIME ZONE,
    statut VARCHAR(20) DEFAULT 'ouvert' CHECK (statut IN ('ouvert', 'en_cours', 'resolu', 'cloture')),
    id_client UUID REFERENCES client(id),
    id_bien UUID REFERENCES bien(id),
    id_transaction UUID REFERENCES transaction(id),
    id_agent_charge UUID REFERENCES utilisateur(id)
);

-- Avis clients
CREATE TABLE avis_client (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    note INTEGER NOT NULL CHECK (note BETWEEN 1 AND 5),
    commentaire TEXT,
    date_avis TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50),
    statut_moderation VARCHAR(20) DEFAULT 'en_attente' CHECK (statut_moderation IN ('en_attente', 'approuve', 'rejete')),
    id_client UUID REFERENCES client(id),
    id_bien UUID REFERENCES bien(id),
    id_agent UUID REFERENCES utilisateur(id),
    id_transaction UUID REFERENCES transaction(id)
);

-- Campagnes marketing
CREATE TABLE campagne (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    type_campagne_id INTEGER REFERENCES type_campagne(id),
    date_debut DATE NOT NULL,
    date_fin DATE,
    budget DECIMAL(10,2),
    statut VARCHAR(20) DEFAULT 'brouillon' CHECK (statut IN ('brouillon', 'active', 'pausee', 'terminee')),
    id_agent_createur UUID REFERENCES utilisateur(id)
);

-- Communications (emails, SMS, etc.)
CREATE TABLE communication (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sujet VARCHAR(255),
    contenu TEXT NOT NULL,
    date_envoi TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    statut VARCHAR(20) DEFAULT 'envoye' CHECK (statut IN ('envoye', 'ouvert', 'clique', 'reponse', 'echec')),
    type_communication_id INTEGER REFERENCES type_communication(id),
    id_campagne UUID REFERENCES campagne(id),
    id_client UUID REFERENCES client(id),
    id_agent_emetteur UUID REFERENCES utilisateur(id)
);

-- Tâches
CREATE TABLE tache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    titre VARCHAR(255) NOT NULL,
    description TEXT,
    date_echeance TIMESTAMP WITH TIME ZONE,
    date_creation TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date_cloture TIMESTAMP WITH TIME ZONE,
    statut VARCHAR(20) DEFAULT 'a_faire' CHECK (statut IN ('a_faire', 'en_cours', 'terminee', 'annulee')),
    priorite VARCHAR(20) DEFAULT 'moyenne' CHECK (priorite IN ('basse', 'moyenne', 'haute', 'urgente')),
    type_tache_id INTEGER REFERENCES type_tache(id),
    id_agent_assigne UUID REFERENCES utilisateur(id),
    id_client UUID REFERENCES client(id),
    id_bien UUID REFERENCES bien(id),
    id_transaction UUID REFERENCES transaction(id)
);

-- Historique des modifications (audit)
CREATE TABLE historique_modification (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_concernee VARCHAR(50) NOT NULL,
    id_enregistrement UUID NOT NULL,
    type_action VARCHAR(10) NOT NULL CHECK (type_action IN ('INSERT', 'UPDATE', 'DELETE')),
    ancienne_valeur JSONB,
    nouvelle_valeur JSONB,
    date_modification TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    id_utilisateur UUID REFERENCES utilisateur(id)
);

-- Favoris clients
CREATE TABLE client_favori (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date_ajout TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    id_client UUID REFERENCES client(id) NOT NULL,
    id_bien UUID REFERENCES bien(id) NOT NULL,
    UNIQUE(id_client, id_bien)
);

-- Permissions des rôles
CREATE TABLE role_permission (
    id SERIAL PRIMARY KEY,
    id_role INTEGER REFERENCES role(id),
    permission VARCHAR(100) NOT NULL,
    UNIQUE(id_role, permission)
);

-- =============================================
-- VUES UTILES
-- =============================================

-- Vue des biens avec détails complets
CREATE VIEW vue_biens_complets AS
SELECT 
    b.id, b.reference, b.titre, b.description, b.prix, b.surface_habitable, 
    b.surface_terrain, b.nb_pieces, b.nb_chambres, b.nb_salles_bain,
    b.annee_construction, b.etat_general, b.dpe, b.ges, b.charges_mensuelles,
    b.taxe_fonciere, b.date_creation, b.date_mise_en_ligne, b.date_vente,
    b.adresse, b.code_postal, b.ville, b.pays, b.geoloc,
    tb.nom AS type_bien, sb.nom AS statut_bien, sb.couleur,
    c.nom AS nom_proprietaire, c.prenom AS prenom_proprietaire,
    u.nom AS nom_agent, u.prenom AS prenom_agent
FROM bien b
LEFT JOIN type_bien tb ON b.type_bien_id = tb.id
LEFT JOIN statut_bien sb ON b.statut_id = sb.id
LEFT JOIN client c ON b.id_proprietaire = c.id
LEFT JOIN utilisateur u ON b.id_agent_charge = u.id;

-- Vue des transactions en cours
CREATE VIEW vue_transactions_encours AS
SELECT 
    t.id, t.reference, t.type_transaction, t.prix_final, 
    t.date_signature, t.date_effet, t.date_fin_prevue,
    st.nom AS statut, st.couleur,
    b.titre AS titre_bien, b.reference AS ref_bien,
    c_ach.nom AS nom_acheteur, c_ach.prenom AS prenom_acheteur,
    c_ven.nom AS nom_vendeur, c_ven.prenom AS prenom_vendeur,
    u.nom AS nom_agent, u.prenom AS prenom_agent
FROM transaction t
JOIN statut_transaction st ON t.statut_id = st.id
JOIN bien b ON t.id_bien = b.id
LEFT JOIN client c_ach ON t.id_acheteur = c_ach.id
LEFT JOIN client c_ven ON t.id_vendeur = c_ven.id
LEFT JOIN utilisateur u ON t.id_agent_charge = u.id
WHERE st.nom NOT IN ('terminee', 'annulee');

-- Vue des performances des agents
CREATE VIEW vue_performances_agents AS
SELECT 
    u.id, u.nom, u.prenom, u.email,
    COUNT(DISTINCT b.id) AS nb_biens_geres,
    COUNT(DISTINCT t.id) AS nb_transactions,
    COUNT(DISTINCT v.id) AS nb_visites,
    COUNT(DISTINCT c.id) AS nb_clients,
    COALESCE(SUM(p.montant), 0) AS total_commissions
FROM utilisateur u
LEFT JOIN bien b ON u.id = b.id_agent_charge
LEFT JOIN transaction t ON u.id = t.id_agent_charge
LEFT JOIN visite v ON u.id = v.id_agent
LEFT JOIN client c ON u.id = c.id_agent_charge
LEFT JOIN paiement p ON u.id = p.id_agent_beneficiaire
WHERE u.actif = true
GROUP BY u.id, u.nom, u.prenom, u.email;

-- Vue des paiements en retard
CREATE VIEW vue_paiements_retard AS
SELECT 
    p.id, p.reference, p.montant, p.date_echeance,
    p.statut, p.notes,
    c.nom AS nom_client, c.prenom AS prenom_client,
    b.titre AS titre_bien,
    u.nom AS nom_agent, u.prenom AS prenom_agent
FROM paiement p
JOIN client c ON p.id_client = c.id
LEFT JOIN bien b ON p.id_bien = b.id
LEFT JOIN utilisateur u ON p.id_agent_beneficiaire = u.id
WHERE p.statut = 'retard' AND p.date_echeance < CURRENT_DATE;

-- Vue des statistiques de marché
CREATE VIEW vue_stats_marche AS
SELECT 
    ville,
    COUNT(id) AS nb_biens_disponibles,
    AVG(prix) AS prix_moyen,
    AVG(surface_habitable) AS surface_moyenne,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY prix) AS prix_median,
    MIN(prix) AS prix_min,
    MAX(prix) AS prix_max
FROM bien
WHERE statut_id = (SELECT id FROM statut_bien WHERE nom = 'disponible')
GROUP BY ville
ORDER BY nb_biens_disponibles DESC;

-- Vue des clients avec critères de recherche
CREATE VIEW vue_clients_critères AS
SELECT 
    c.id, c.nom, c.prenom, c.email, c.telephone,
    c.type_client, c.budget, c.criteres_recherche,
    COUNT(cf.id_bien) AS nb_favoris,
    MAX(v.date_visite) AS derniere_visite
FROM client c
LEFT JOIN client_favori cf ON c.id = cf.id_client
LEFT JOIN visite v ON c.id = v.id_client
GROUP BY c.id, c.nom, c.prenom, c.email, c.telephone, c.type_client, c.budget, c.criteres_recherche;

-- =============================================
-- FONCTIONS ET PROCÉDURES STOCKÉES
-- =============================================

-- Fonction pour calculer la commission d'un agent
CREATE OR REPLACE FUNCTION calculer_commission(
    p_montant_transaction DECIMAL,
    p_taux_commission DECIMAL,
    p_bonus BOOLEAN DEFAULT false
) RETURNS DECIMAL AS $$
DECLARE
    v_commission DECIMAL;
    v_bonus DECIMAL := 0;
BEGIN
    v_commission := p_montant_transaction * (p_taux_commission / 100);
    
    IF p_bonus THEN
        v_bonus := v_commission * 0.1; -- 10% de bonus
    END IF;
    
    RETURN v_commission + v_bonus;
END;
$$ LANGUAGE plpgsql;

-- Procédure pour créer une transaction à partir d'une offre acceptée
CREATE OR REPLACE procedure creer_transaction_de_offre(
    p_id_offre UUID,
    p_id_agent UUID
) LANGUAGE plpgsql AS $$
DECLARE
    v_offre RECORD;
    v_id_transaction UUID;
BEGIN
    -- Récupérer les informations de l'offre
    SELECT * INTO v_offre FROM offre WHERE id = p_id_offre;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Offre non trouvée';
    END IF;
    
    -- Créer la transaction
    v_id_transaction := uuid_generate_v4();
    
    INSERT INTO transaction (
        id, reference, type_transaction, prix_final, 
        date_signature, statut_id, id_bien, id_acheteur, 
        id_vendeur, id_agent_charge
    ) VALUES (
        v_id_transaction, 
        'TRANS-' || substring(v_id_transaction::text, 1, 8),
        CASE WHEN v_offre.type_transaction = 'location' THEN 'location' ELSE 'vente' END,
        v_offre.montant,
        NOW(),
        (SELECT id FROM statut_transaction WHERE nom = 'en_cours'),
        v_offre.id_bien,
        v_offre.id_client,
        (SELECT id_proprietaire FROM bien WHERE id = v_offre.id_bien),
        p_id_agent
    );
    
    -- Mettre à jour le statut de l'offre
    UPDATE offre SET statut = 'acceptee', id_transaction = v_id_transaction WHERE id = p_id_offre;
    
    -- Mettre à jour le statut du bien
    UPDATE bien SET statut_id = (SELECT id FROM statut_bien WHERE nom = 'sous_compromis') WHERE id = v_offre.id_bien;
    
    -- Créer une tâche de suivi
    INSERT INTO tache (
        id, titre, description, date_echeance, type_tache_id, 
        id_agent_assigne, id_transaction
    ) VALUES (
        uuid_generate_v4(),
        'Suivi transaction ' || (SELECT reference FROM transaction WHERE id = v_id_transaction),
        'Assurer le suivi des étapes administratives de la transaction',
        NOW() + INTERVAL '7 days',
        (SELECT id FROM type_tache WHERE nom = 'suivi_administratif'),
        p_id_agent,
        v_id_transaction
    );
    
    COMMIT;
END;
$$;

-- Fonction pour estimer le prix d'un bien (IA simulée)
CREATE OR REPLACE FUNCTION estimer_prix_bien(
    p_surface DECIMAL,
    p_nb_pieces INTEGER,
    p_ville VARCHAR,
    p_type_bien VARCHAR
) RETURNS DECIMAL AS $$
DECLARE
    v_prix_moyen DECIMAL;
    v_prix_estime DECIMAL;
BEGIN
    -- Calcul basé sur des statistiques de marché (simplifié)
    SELECT AVG(prix / surface_habitable) INTO v_prix_moyen
    FROM bien b
    JOIN type_bien tb ON b.type_bien_id = tb.id
    WHERE b.ville = p_ville 
      AND tb.nom = p_type_bien
      AND b.statut_id = (SELECT id FROM statut_bien WHERE nom = 'vendu');
    
    IF v_prix_moyen IS NULL THEN
        -- Valeur par défaut si aucune donnée
        v_prix_moyen := 3000; -- 3000€/m²
    END IF;
    
    -- Ajustement selon le nombre de pièces
    v_prix_estime := p_surface * v_prix_moyen * (1 + (p_nb_pieces - 2) * 0.05);
    
    RETURN ROUND(v_prix_estime, 2);
END;
$$ LANGUAGE plpgsql;

-- Procédure pour gérer les relances de paiement
CREATE OR REPLACE procedure relancer_paiements_retard()
LANGUAGE plpgsql AS $$
DECLARE
    v_paiement RECORD;
BEGIN
    FOR v_paiement IN 
        SELECT * FROM paiement 
        WHERE statut = 'retard' 
          AND date_echeance < CURRENT_DATE - INTERVAL '3 days'
    LOOP
        -- Créer une communication de relance
        INSERT INTO communication (
            id, sujet, contenu, type_communication_id, 
            id_client, id_agent_emetteur
        ) VALUES (
            uuid_generate_v4(),
            'Relance paiement - ' || v_paiement.reference,
            'Bonjour, nous vous rappelons que votre paiement de ' || v_paiement.montant || 
            '€ est en retard depuis le ' || v_paiement.date_echeance || 
            '. Merci de régulariser votre situation au plus vite.',
            (SELECT id FROM type_communication WHERE nom = 'email_relance'),
            v_paiement.id_client,
            (SELECT id_agent_charge FROM bien WHERE id = v_paiement.id_bien)
        );
        
        -- Mettre à jour le statut
        UPDATE paiement SET statut = 'relance_envoyee' WHERE id = v_paiement.id;
    END LOOP;
    
    COMMIT;
END;
$$;

-- Fonction pour calculer le score d'un lead
CREATE OR REPLACE FUNCTION calculer_score_lead(
    p_budget DECIMAL,
    p_urgence BOOLEAN,
    p_source VARCHAR
) RETURNS INTEGER AS $$
DECLARE
    v_score INTEGER := 0;
BEGIN
    -- Score selon le budget
    IF p_budget > 500000 THEN
        v_score := v_score + 30;
    ELSIF p_budget > 300000 THEN
        v_score := v_score + 20;
    ELSIF p_budget > 150000 THEN
        v_score := v_score + 10;
    END IF;
    
    -- Score selon l'urgence
    IF p_urgence THEN
        v_score := v_score + 40;
    END IF;
    
    -- Score selon la source
    IF p_source = 'recommandation' THEN
        v_score := v_score + 30;
    ELSIF p_source = 'site_web' THEN
        v_score := v_score + 20;
    ELSIF p_source = 'reseaux_sociaux' THEN
        v_score := v_score + 15;
    END IF;
    
    RETURN LEAST(v_score, 100); -- Maximum 100
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger pour l'historique des modifications
CREATE OR REPLACE FUNCTION audit_modifications()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO historique_modification (
            table_concernee, id_enregistrement, type_action, 
            ancienne_valeur, date_modification, id_utilisateur
        ) VALUES (
            TG_TABLE_NAME, OLD.id, TG_OP, 
            to_jsonb(OLD), NOW(), current_setting('app.current_user_id', true)::UUID
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO historique_modification (
            table_concernee, id_enregistrement, type_action, 
            ancienne_valeur, nouvelle_valeur, date_modification, id_utilisateur
        ) VALUES (
            TG_TABLE_NAME, NEW.id, TG_OP, 
            to_jsonb(OLD), to_jsonb(NEW), NOW(), current_setting('app.current_user_id', true)::UUID
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO historique_modification (
            table_concernee, id_enregistrement, type_action, 
            nouvelle_valeur, date_modification, id_utilisateur
        ) VALUES (
            TG_TABLE_NAME, NEW.id, TG_OP, 
            to_jsonb(NEW), NOW(), current_setting('app.current_user_id', true)::UUID
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Appliquer le trigger aux tables principales
CREATE TRIGGER audit_bien 
    AFTER INSERT OR UPDATE OR DELETE ON bien 
    FOR EACH ROW EXECUTE FUNCTION audit_modifications();

CREATE TRIGGER audit_client 
    AFTER INSERT OR UPDATE OR DELETE ON client 
    FOR EACH ROW EXECUTE FUNCTION audit_modifications();

CREATE TRIGGER audit_transaction 
    AFTER INSERT OR UPDATE OR DELETE ON transaction 
    FOR EACH ROW EXECUTE FUNCTION audit_modifications();

CREATE TRIGGER audit_mandat 
    AFTER INSERT OR UPDATE OR DELETE ON mandat 
    FOR EACH ROW EXECUTE FUNCTION audit_modifications();

-- Trigger pour mettre à jour la géolocalisation
CREATE OR REPLACE FUNCTION update_geoloc()
RETURNS TRIGGER AS $$
BEGIN
    -- Simulation de géocodage (à remplacer par un vrai service)
    NEW.geoloc := point(random()*100, random()*100);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_geoloc_bien 
    BEFORE INSERT OR UPDATE ON bien 
    FOR EACH ROW EXECUTE FUNCTION update_geoloc();

-- Trigger pour la validation des mandats
CREATE OR REPLACE FUNCTION valider_mandat()
RETURNS TRIGGER AS $$
BEGIN
    -- Vérifier que le bien n'a pas déjà un mandat actif
    IF EXISTS (
        SELECT 1 FROM mandat 
        WHERE id_bien = NEW.id_bien 
          AND actif = true 
          AND id <> NEW.id
    ) THEN
        RAISE EXCEPTION 'Ce bien a déjà un mandat actif';
    END IF;
    
    -- Vérifier que le propriétaire correspond
    IF (SELECT id_proprietaire FROM bien WHERE id = NEW.id_bien) <> NEW.id_proprietaire THEN
        RAISE EXCEPTION 'Le propriétaire spécifié ne correspond pas au bien';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_valider_mandat 
    BEFORE INSERT OR UPDATE ON mandat 
    FOR EACH ROW EXECUTE FUNCTION valider_mandat();

-- Trigger pour la création automatique de tâches
CREATE OR REPLACE FUNCTION creer_tache_visite()
RETURNS TRIGGER AS $$
BEGIN
    -- Créer une tâche de préparation de visite
    INSERT INTO tache (
        id, titre, description, date_echeance, type_tache_id, 
        id_agent_assigne, id_client, id_bien
    ) VALUES (
        uuid_generate_v4(),
        'Préparer visite - ' || (SELECT titre FROM bien WHERE id = NEW.id_bien),
        'Préparer la visite pour le client ' || (SELECT nom FROM client WHERE id = NEW.id_client),
        NEW.date_visite - INTERVAL '1 day',
        (SELECT id FROM type_tache WHERE nom = 'preparation_visite'),
        NEW.id_agent,
        NEW.id_client,
        NEW.id_bien
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_tache_visite 
    AFTER INSERT ON visite 
    FOR EACH ROW EXECUTE FUNCTION creer_tache_visite();

-- =============================================
-- INDEXATIONS POUR PERFORMANCES
-- =============================================

-- Index sur les colonnes fréquemment recherchées
CREATE INDEX idx_bien_ville ON bien(ville);
CREATE INDEX idx_bien_prix ON bien(prix);
CREATE INDEX idx_bien_statut ON bien(statut_id);
CREATE INDEX idx_client_nom ON client(nom, prenom);
CREATE INDEX idx_client_type ON client(type_client);
CREATE INDEX idx_transaction_statut ON transaction(statut_id);
CREATE INDEX idx_transaction_date ON transaction(date_signature);
CREATE INDEX idx_paiement_statut ON paiement(statut);
CREATE INDEX idx_paiement_echeance ON paiement(date_echeance);
CREATE INDEX idx_visite_date ON visite(date_visite);
CREATE INDEX idx_tache_echeance ON tache(date_echeance);
CREATE INDEX idx_tache_statut ON tache(statut);

-- Index sur les colonnes JSONB
CREATE INDEX idx_client_criteres ON client USING GIN(criteres_recherche);
CREATE INDEX idx_bien_caracteristiques ON bien USING GIN(caracteristiques_supplementaires);

-- Index full-text pour la recherche
CREATE INDEX idx_bien_search ON bien USING GIN(to_tsvector('french', titre || ' ' || description || ' ' || ville));
CREATE INDEX idx_client_search ON client USING GIN(to_tsvector('french', nom || ' ' || prenom || ' ' || COALESCE(notes, '')));

-- =============================================
-- SÉCURITÉ
-- =============================================

-- Rôle par défaut pour les utilisateurs de l'application
CREATE ROLE app_user;
GRANT CONNECT ON DATABASE immobilier TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO app_user;

-- Rôle en lecture seule pour les rapports
CREATE ROLE reporting_user;
GRANT CONNECT ON DATABASE immobilier TO reporting_user;
GRANT USAGE ON SCHEMA public TO reporting_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO reporting_user;

-- Politique de sécurité pour les données sensibles
ALTER TABLE utilisateur DISABLE ROW LEVEL SECURITY;
CREATE POLICY utilisateur_isolation ON utilisateur
    FOR ALL TO app_user
    USING (id = current_setting('app.current_user_id', true)::UUID OR 
           current_setting('app.current_user_role', true) = 'admin');

-- Activer RLS
ALTER TABLE utilisateur ENABLE ROW LEVEL SECURITY;
